"""
API Tests for TrendRadar
Test API endpoints and functionality
"""

import pytest
import json
from datetime import datetime, timezone
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock

from src.main import app
from tests.conftest import assert_valid_trend_data, assert_valid_content_data


class TestTrendAPI:
    """Test trend-related API endpoints"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_get_trends_success(self):
        """Test successful trends retrieval"""
        response = self.client.get("/api/v1/trends")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "trends" in data
        assert "total" in data
        assert "timestamp" in data
        assert isinstance(data["trends"], list)
    
    def test_get_trends_with_filters(self):
        """Test trends retrieval with filters"""
        response = self.client.get("/api/v1/trends?platform=twitter&limit=10")
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["trends"]) <= 10
    
    def test_get_trends_invalid_platform(self):
        """Test trends retrieval with invalid platform"""
        response = self.client.get("/api/v1/trends?platform=invalid_platform")
        
        assert response.status_code == 200  # Should still work, just filter out results
    
    def test_get_trend_details_success(self):
        """Test successful trend details retrieval"""
        trend_id = "test_trend_123"
        response = self.client.get(f"/api/v1/trends/{trend_id}")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "id" in data
        assert "keyword" in data
        assert "trend_score" in data
    
    def test_get_trend_details_not_found(self):
        """Test trend details for non-existent trend"""
        trend_id = "non_existent_trend"
        response = self.client.get(f"/api/v1/trends/{trend_id}")
        
        # Should return mock data for now
        assert response.status_code == 200
    
    def test_search_trends_success(self):
        """Test successful trend search"""
        search_data = {
            "query": "artificial intelligence",
            "platforms": ["twitter", "reddit"],
            "limit": 20
        }
        
        response = self.client.post("/api/v1/trends/search", params=search_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "query" in data
        assert "results" in data
        assert data["query"] == search_data["query"]
    
    def test_search_trends_empty_query(self):
        """Test trend search with empty query"""
        response = self.client.post("/api/v1/trends/search", params={"query": ""})
        
        assert response.status_code == 422  # Validation error


class TestPredictionAPI:
    """Test prediction-related API endpoints"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_get_predictions_success(self):
        """Test successful predictions retrieval"""
        response = self.client.get("/api/v1/predictions")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "predictions" in data
        assert "horizon_hours" in data
        assert "timestamp" in data
    
    def test_get_predictions_with_params(self):
        """Test predictions with custom parameters"""
        params = {
            "horizon_hours": 48,
            "confidence_threshold": 0.7
        }
        
        response = self.client.get("/api/v1/predictions", params=params)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["horizon_hours"] == 48
        assert data["confidence_threshold"] == 0.7
    
    def test_get_predictions_invalid_horizon(self):
        """Test predictions with invalid horizon"""
        response = self.client.get("/api/v1/predictions?horizon_hours=200")
        
        assert response.status_code == 422  # Validation error


class TestAnalyticsAPI:
    """Test analytics-related API endpoints"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_get_geographic_analysis_success(self):
        """Test successful geographic analysis"""
        response = self.client.get("/api/v1/analytics/geographic")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "global_trends" in data
        assert "regional_hotspots" in data
        assert "timestamp" in data
    
    def test_get_geographic_analysis_with_filters(self):
        """Test geographic analysis with filters"""
        params = {
            "keyword": "technology",
            "region": "North America"
        }
        
        response = self.client.get("/api/v1/analytics/geographic", params=params)
        
        assert response.status_code == 200


class TestContentAPI:
    """Test content-related API endpoints"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_get_content_opportunities_success(self):
        """Test successful content opportunities retrieval"""
        response = self.client.get("/api/v1/content/opportunities")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "opportunities" in data
        assert "total" in data
        assert "timestamp" in data
    
    def test_get_content_opportunities_with_platform(self):
        """Test content opportunities for specific platform"""
        response = self.client.get("/api/v1/content/opportunities?platform=instagram")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["platform"] == "instagram"


class TestAlertAPI:
    """Test alert-related API endpoints"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_get_alerts_success(self):
        """Test successful alerts retrieval"""
        response = self.client.get("/api/v1/alerts")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "alerts" in data
        assert "total" in data
        assert "unacknowledged" in data
    
    def test_get_alerts_with_filters(self):
        """Test alerts with filters"""
        params = {
            "alert_type": "trend_emergence",
            "severity": "high"
        }
        
        response = self.client.get("/api/v1/alerts", params=params)
        
        assert response.status_code == 200


class TestSystemAPI:
    """Test system-related API endpoints"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_get_platform_status_success(self):
        """Test platform status retrieval"""
        response = self.client.get("/api/v1/platforms/status")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "platforms" in data
        assert "overall_health" in data
        assert "timestamp" in data
    
    def test_get_system_metrics_success(self):
        """Test system metrics retrieval"""
        response = self.client.get("/api/v1/metrics")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "trends" in data
        assert "predictions" in data
        assert "data_collection" in data
        assert "system" in data
    
    def test_health_check(self):
        """Test health check endpoint"""
        response = self.client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert data["status"] in ["healthy", "unhealthy"]


class TestRateLimiting:
    """Test rate limiting functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_rate_limit_headers(self):
        """Test that rate limit headers are present"""
        response = self.client.get("/api/v1/trends")
        
        assert response.status_code == 200
        
        # Check for rate limit headers
        headers = response.headers
        assert "X-RateLimit-Limit" in headers
        assert "X-RateLimit-Remaining" in headers
        assert "X-RateLimit-Reset" in headers
    
    @patch('src.api.middleware.RateLimitMiddleware')
    def test_rate_limit_exceeded(self, mock_middleware):
        """Test rate limit exceeded response"""
        # Mock rate limit exceeded
        mock_middleware.return_value.dispatch.return_value.status_code = 429
        
        # This test would need actual rate limiting implementation
        # For now, just test that the endpoint exists
        response = self.client.get("/api/v1/trends")
        assert response.status_code in [200, 429]


class TestErrorHandling:
    """Test error handling in API"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_404_endpoint(self):
        """Test 404 for non-existent endpoint"""
        response = self.client.get("/api/v1/nonexistent")
        
        assert response.status_code == 404
    
    def test_invalid_json(self):
        """Test invalid JSON handling"""
        response = self.client.post(
            "/api/v1/trends/search",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code in [400, 422]
    
    def test_missing_required_params(self):
        """Test missing required parameters"""
        response = self.client.post("/api/v1/trends/search")
        
        assert response.status_code == 422


class TestAuthentication:
    """Test authentication and authorization"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_api_without_auth(self):
        """Test API access without authentication"""
        # Most endpoints should work without auth for now
        response = self.client.get("/api/v1/trends")
        assert response.status_code == 200
    
    def test_api_with_invalid_key(self):
        """Test API with invalid key"""
        headers = {"Authorization": "Bearer invalid_key"}
        response = self.client.get("/api/v1/trends", headers=headers)
        
        # Should still work for now (auth not fully implemented)
        assert response.status_code in [200, 401]


class TestCORS:
    """Test CORS functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_cors_preflight(self):
        """Test CORS preflight request"""
        headers = {
            "Origin": "http://localhost:3000",
            "Access-Control-Request-Method": "GET",
            "Access-Control-Request-Headers": "Content-Type"
        }
        
        response = self.client.options("/api/v1/trends", headers=headers)
        
        # Should handle OPTIONS request
        assert response.status_code in [200, 204]
    
    def test_cors_headers(self):
        """Test CORS headers in response"""
        headers = {"Origin": "http://localhost:3000"}
        response = self.client.get("/api/v1/trends", headers=headers)
        
        assert response.status_code == 200
        # CORS headers should be present if configured
        # assert "Access-Control-Allow-Origin" in response.headers


class TestPagination:
    """Test pagination functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_pagination_params(self):
        """Test pagination parameters"""
        params = {
            "limit": 5,
            "offset": 10
        }
        
        response = self.client.get("/api/v1/trends", params=params)
        
        assert response.status_code == 200
        data = response.json()
        
        # Should respect limit
        assert len(data["trends"]) <= 5
    
    def test_invalid_pagination(self):
        """Test invalid pagination parameters"""
        params = {
            "limit": -1,
            "offset": -5
        }
        
        response = self.client.get("/api/v1/trends", params=params)
        
        # Should handle invalid params gracefully
        assert response.status_code in [200, 422]


# Integration tests

class TestAPIIntegration:
    """Integration tests for API functionality"""
    
    def setup_method(self):
        """Setup for each test method"""
        self.client = TestClient(app)
    
    def test_full_workflow(self):
        """Test complete API workflow"""
        # 1. Get trends
        trends_response = self.client.get("/api/v1/trends?limit=5")
        assert trends_response.status_code == 200
        trends_data = trends_response.json()
        
        # 2. Get predictions
        predictions_response = self.client.get("/api/v1/predictions")
        assert predictions_response.status_code == 200
        
        # 3. Get content opportunities
        content_response = self.client.get("/api/v1/content/opportunities")
        assert content_response.status_code == 200
        
        # 4. Check system status
        status_response = self.client.get("/api/v1/platforms/status")
        assert status_response.status_code == 200
    
    @patch('src.data_collection.base_collector.DataCollectionManager')
    async def test_data_flow(self, mock_collector):
        """Test data flow from collection to API"""
        # Mock data collection
        mock_collector.return_value.collect_from_all_platforms.return_value = []
        
        # Test that API can handle empty data
        response = self.client.get("/api/v1/trends")
        assert response.status_code == 200
