#!/usr/bin/env python3
"""
Database Setup Script for TrendRadar
Creates database tables and initial configuration
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.database import init_database, DatabaseManager, engine
from config.settings import settings
from sqlalchemy import text
import click

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_database_schema():
    """Create database schema and tables"""
    try:
        # Initialize database connection
        init_database()
        
        # SQL commands to create tables
        create_tables_sql = """
        -- Users table
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(255) UNIQUE NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Trends table
        CREATE TABLE IF NOT EXISTS trends (
            id SERIAL PRIMARY KEY,
            keyword VARCHAR(255) NOT NULL,
            platform VARCHAR(50) NOT NULL,
            trend_score FLOAT DEFAULT 0.0,
            viral_probability FLOAT DEFAULT 0.0,
            lifecycle_stage VARCHAR(50) DEFAULT 'emerging',
            mentions INTEGER DEFAULT 0,
            velocity FLOAT DEFAULT 0.0,
            acceleration FLOAT DEFAULT 0.0,
            engagement_rate FLOAT DEFAULT 0.0,
            geographic_spread INTEGER DEFAULT 0,
            sentiment_score FLOAT DEFAULT 0.0,
            peak_prediction TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(keyword, platform, DATE(created_at))
        );

        -- Content table
        CREATE TABLE IF NOT EXISTS content (
            id VARCHAR(255) PRIMARY KEY,
            platform VARCHAR(50) NOT NULL,
            content_type VARCHAR(50) NOT NULL,
            text TEXT,
            author_id VARCHAR(255),
            author_username VARCHAR(255),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL,
            engagement_metrics JSONB DEFAULT '{}',
            hashtags TEXT[] DEFAULT '{}',
            mentions TEXT[] DEFAULT '{}',
            urls TEXT[] DEFAULT '{}',
            location VARCHAR(255),
            language VARCHAR(10) DEFAULT 'en',
            media_urls TEXT[] DEFAULT '{}',
            parent_id VARCHAR(255),
            raw_data JSONB DEFAULT '{}',
            processed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Alerts table
        CREATE TABLE IF NOT EXISTS alerts (
            id VARCHAR(255) PRIMARY KEY,
            type VARCHAR(50) NOT NULL,
            severity VARCHAR(20) NOT NULL,
            keyword VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            platforms TEXT[] DEFAULT '{}',
            metrics JSONB DEFAULT '{}',
            threshold_values JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            acknowledged BOOLEAN DEFAULT FALSE,
            acknowledged_at TIMESTAMP WITH TIME ZONE,
            acknowledged_by VARCHAR(255),
            expires_at TIMESTAMP WITH TIME ZONE,
            metadata JSONB DEFAULT '{}'
        );

        -- Hashtags table
        CREATE TABLE IF NOT EXISTS hashtags (
            id SERIAL PRIMARY KEY,
            hashtag VARCHAR(255) UNIQUE NOT NULL,
            total_mentions INTEGER DEFAULT 0,
            platforms TEXT[] DEFAULT '{}',
            first_seen TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            last_seen TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            trend_score FLOAT DEFAULT 0.0
        );

        -- Geographic data table
        CREATE TABLE IF NOT EXISTS geographic_data (
            id SERIAL PRIMARY KEY,
            keyword VARCHAR(255) NOT NULL,
            region VARCHAR(100) NOT NULL,
            country VARCHAR(10),
            mentions INTEGER DEFAULT 0,
            velocity FLOAT DEFAULT 0.0,
            sentiment_score FLOAT DEFAULT 0.0,
            timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(keyword, region, DATE(timestamp))
        );

        -- Predictions table
        CREATE TABLE IF NOT EXISTS predictions (
            id SERIAL PRIMARY KEY,
            keyword VARCHAR(255) NOT NULL,
            prediction_type VARCHAR(50) NOT NULL,
            predicted_value FLOAT NOT NULL,
            confidence FLOAT NOT NULL,
            horizon_hours INTEGER NOT NULL,
            features JSONB DEFAULT '{}',
            model_version VARCHAR(50),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            actual_value FLOAT,
            accuracy_score FLOAT
        );

        -- Metrics table
        CREATE TABLE IF NOT EXISTS metrics (
            id SERIAL PRIMARY KEY,
            metric_name VARCHAR(100) NOT NULL,
            metric_value FLOAT NOT NULL,
            labels JSONB DEFAULT '{}',
            timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );

        -- Create indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_trends_keyword ON trends(keyword);
        CREATE INDEX IF NOT EXISTS idx_trends_platform ON trends(platform);
        CREATE INDEX IF NOT EXISTS idx_trends_created_at ON trends(created_at);
        CREATE INDEX IF NOT EXISTS idx_trends_trend_score ON trends(trend_score);

        CREATE INDEX IF NOT EXISTS idx_content_platform ON content(platform);
        CREATE INDEX IF NOT EXISTS idx_content_created_at ON content(created_at);
        CREATE INDEX IF NOT EXISTS idx_content_hashtags ON content USING GIN(hashtags);
        CREATE INDEX IF NOT EXISTS idx_content_author_id ON content(author_id);

        CREATE INDEX IF NOT EXISTS idx_alerts_type ON alerts(type);
        CREATE INDEX IF NOT EXISTS idx_alerts_severity ON alerts(severity);
        CREATE INDEX IF NOT EXISTS idx_alerts_created_at ON alerts(created_at);
        CREATE INDEX IF NOT EXISTS idx_alerts_acknowledged ON alerts(acknowledged);

        CREATE INDEX IF NOT EXISTS idx_hashtags_hashtag ON hashtags(hashtag);
        CREATE INDEX IF NOT EXISTS idx_hashtags_trend_score ON hashtags(trend_score);

        CREATE INDEX IF NOT EXISTS idx_geographic_keyword ON geographic_data(keyword);
        CREATE INDEX IF NOT EXISTS idx_geographic_region ON geographic_data(region);
        CREATE INDEX IF NOT EXISTS idx_geographic_timestamp ON geographic_data(timestamp);

        CREATE INDEX IF NOT EXISTS idx_predictions_keyword ON predictions(keyword);
        CREATE INDEX IF NOT EXISTS idx_predictions_type ON predictions(prediction_type);
        CREATE INDEX IF NOT EXISTS idx_predictions_created_at ON predictions(created_at);

        CREATE INDEX IF NOT EXISTS idx_metrics_name ON metrics(metric_name);
        CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON metrics(timestamp);
        """
        
        # Execute SQL commands
        with engine.connect() as connection:
            # Split and execute each statement
            statements = [stmt.strip() for stmt in create_tables_sql.split(';') if stmt.strip()]
            
            for statement in statements:
                try:
                    connection.execute(text(statement))
                    connection.commit()
                except Exception as e:
                    logger.error(f"Error executing statement: {e}")
                    logger.error(f"Statement: {statement[:100]}...")
                    continue
        
        logger.info("Database schema created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create database schema: {e}")
        return False


def create_initial_data():
    """Create initial data and configuration"""
    try:
        with engine.connect() as connection:
            # Insert default alert rules
            insert_alert_rules = """
            INSERT INTO alerts (id, type, severity, keyword, message, platforms, created_at, acknowledged)
            VALUES 
                ('default_trend_emergence', 'trend_emergence', 'high', 'system', 'Default trend emergence rule', '{}', CURRENT_TIMESTAMP, TRUE),
                ('default_velocity_change', 'velocity_change', 'medium', 'system', 'Default velocity change rule', '{}', CURRENT_TIMESTAMP, TRUE)
            ON CONFLICT (id) DO NOTHING;
            """
            
            # Insert sample hashtags
            insert_hashtags = """
            INSERT INTO hashtags (hashtag, total_mentions, platforms, trend_score)
            VALUES 
                ('ai', 0, '{"twitter", "reddit"}', 0.0),
                ('technology', 0, '{"twitter", "youtube"}', 0.0),
                ('trending', 0, '{"twitter", "instagram"}', 0.0)
            ON CONFLICT (hashtag) DO NOTHING;
            """
            
            connection.execute(text(insert_alert_rules))
            connection.execute(text(insert_hashtags))
            connection.commit()
        
        logger.info("Initial data created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create initial data: {e}")
        return False


def verify_database_setup():
    """Verify database setup is correct"""
    try:
        with engine.connect() as connection:
            # Check if tables exist
            tables_query = """
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name;
            """
            
            result = connection.execute(text(tables_query))
            tables = [row[0] for row in result]
            
            expected_tables = [
                'alerts', 'content', 'geographic_data', 'hashtags', 
                'metrics', 'predictions', 'trends', 'users'
            ]
            
            missing_tables = set(expected_tables) - set(tables)
            if missing_tables:
                logger.error(f"Missing tables: {missing_tables}")
                return False
            
            logger.info(f"Found {len(tables)} tables: {', '.join(tables)}")
            
            # Check if we can insert and query data
            test_query = "SELECT COUNT(*) FROM hashtags;"
            result = connection.execute(text(test_query))
            count = result.scalar()
            logger.info(f"Hashtags table has {count} records")
            
        logger.info("Database verification completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Database verification failed: {e}")
        return False


@click.command()
@click.option('--drop-existing', is_flag=True, help='Drop existing tables before creating new ones')
@click.option('--skip-initial-data', is_flag=True, help='Skip creating initial data')
@click.option('--verify-only', is_flag=True, help='Only verify database setup')
def main(drop_existing, skip_initial_data, verify_only):
    """Setup TrendRadar database"""
    logger.info("Starting TrendRadar database setup...")
    
    if verify_only:
        success = verify_database_setup()
        sys.exit(0 if success else 1)
    
    try:
        # Drop existing tables if requested
        if drop_existing:
            logger.warning("Dropping existing tables...")
            with engine.connect() as connection:
                drop_tables = """
                DROP TABLE IF EXISTS metrics CASCADE;
                DROP TABLE IF EXISTS predictions CASCADE;
                DROP TABLE IF EXISTS geographic_data CASCADE;
                DROP TABLE IF EXISTS hashtags CASCADE;
                DROP TABLE IF EXISTS alerts CASCADE;
                DROP TABLE IF EXISTS content CASCADE;
                DROP TABLE IF EXISTS trends CASCADE;
                DROP TABLE IF EXISTS users CASCADE;
                """
                connection.execute(text(drop_tables))
                connection.commit()
            logger.info("Existing tables dropped")
        
        # Create database schema
        if not create_database_schema():
            logger.error("Failed to create database schema")
            sys.exit(1)
        
        # Create initial data
        if not skip_initial_data:
            if not create_initial_data():
                logger.error("Failed to create initial data")
                sys.exit(1)
        
        # Verify setup
        if not verify_database_setup():
            logger.error("Database verification failed")
            sys.exit(1)
        
        logger.info("TrendRadar database setup completed successfully!")
        logger.info("You can now start the application with: python src/main.py")
        
    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
