"""
Alert Manager for TrendRadar
Manages trend alerts, notifications, and monitoring
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
import json

from config.settings import settings
from config.database import CacheManager
from src.utils.constants import AlertType, AlertSeverity, Platform, ALERT_CONFIG
from src.utils.helpers import get_current_timestamp

logger = logging.getLogger(__name__)


@dataclass
class Alert:
    """Represents a trend alert"""
    id: str
    type: AlertType
    severity: AlertSeverity
    keyword: str
    message: str
    platforms: List[Platform]
    metrics: Dict[str, Any]
    threshold_values: Dict[str, float]
    created_at: datetime
    acknowledged: bool = False
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    expires_at: Optional[datetime] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class AlertRule:
    """Defines conditions for triggering alerts"""
    id: str
    name: str
    alert_type: AlertType
    conditions: Dict[str, Any]
    severity: AlertSeverity
    enabled: bool = True
    cooldown_minutes: int = 5
    platforms: List[Platform] = None
    keywords: List[str] = None
    
    def __post_init__(self):
        if self.platforms is None:
            self.platforms = []
        if self.keywords is None:
            self.keywords = []


class AlertManager:
    """Main alert management system"""
    
    def __init__(self):
        self.cache_manager = CacheManager()
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_rules: Dict[str, AlertRule] = {}
        self.alert_history: List[Alert] = []
        self.notification_handlers: List[Callable] = []
        self.is_monitoring = False
        self.last_check_time = None
        
        # Initialize default alert rules
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """Initialize default alert rules"""
        default_rules = [
            AlertRule(
                id="trend_emergence",
                name="Trend Emergence Detection",
                alert_type=AlertType.TREND_EMERGENCE,
                conditions={
                    "trend_score": {"min": 0.7},
                    "velocity": {"min": 20.0},
                    "mentions": {"min": 50}
                },
                severity=AlertSeverity.HIGH,
                cooldown_minutes=15
            ),
            AlertRule(
                id="velocity_spike",
                name="Velocity Spike Detection",
                alert_type=AlertType.VELOCITY_CHANGE,
                conditions={
                    "velocity_change": {"min": 50.0},
                    "acceleration": {"min": 5.0}
                },
                severity=AlertSeverity.MEDIUM,
                cooldown_minutes=10
            ),
            AlertRule(
                id="viral_prediction",
                name="Viral Content Prediction",
                alert_type=AlertType.VIRAL_PREDICTION,
                conditions={
                    "viral_probability": {"min": 0.8},
                    "engagement_rate": {"min": 5.0}
                },
                severity=AlertSeverity.HIGH,
                cooldown_minutes=30
            ),
            AlertRule(
                id="geographic_spread",
                name="Geographic Spread Alert",
                alert_type=AlertType.GEOGRAPHIC_SPREAD,
                conditions={
                    "geographic_spread": {"min": 5},
                    "spread_velocity": {"min": 2.0}
                },
                severity=AlertSeverity.MEDIUM,
                cooldown_minutes=20
            ),
            AlertRule(
                id="saturation_warning",
                name="Trend Saturation Warning",
                alert_type=AlertType.SATURATION_WARNING,
                conditions={
                    "trend_score": {"max": 0.3},
                    "velocity": {"max": 5.0},
                    "stage": {"equals": "declining"}
                },
                severity=AlertSeverity.LOW,
                cooldown_minutes=60
            )
        ]
        
        for rule in default_rules:
            self.alert_rules[rule.id] = rule
    
    async def initialize(self):
        """Initialize the alert manager"""
        try:
            # Load alert rules from cache/database
            await self._load_alert_rules()
            
            # Load active alerts
            await self._load_active_alerts()
            
            logger.info(f"Alert manager initialized with {len(self.alert_rules)} rules")
            
        except Exception as e:
            logger.error(f"Failed to initialize alert manager: {e}")
            raise
    
    async def start_monitoring(self):
        """Start alert monitoring"""
        if self.is_monitoring:
            logger.warning("Alert monitoring already running")
            return
        
        self.is_monitoring = True
        logger.info("Starting alert monitoring...")
        
        # Start monitoring loop
        asyncio.create_task(self._monitoring_loop())
    
    async def stop_monitoring(self):
        """Stop alert monitoring"""
        self.is_monitoring = False
        logger.info("Alert monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                await self._check_alerts()
                await self._cleanup_expired_alerts()
                
                # Wait before next check
                await asyncio.sleep(ALERT_CONFIG.get("check_interval", 60))
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in alert monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _check_alerts(self):
        """Check for alert conditions"""
        try:
            # This would typically get trend data from the database
            # For now, we'll simulate with mock data
            current_trends = await self._get_current_trends()
            
            for trend in current_trends:
                await self._evaluate_trend_alerts(trend)
            
            self.last_check_time = get_current_timestamp()
            
        except Exception as e:
            logger.error(f"Error checking alerts: {e}")
    
    async def _get_current_trends(self) -> List[Dict[str, Any]]:
        """Get current trend data for alert evaluation"""
        # Mock trend data - in production, this would query the database
        return [
            {
                "keyword": "ai_breakthrough",
                "trend_score": 0.85,
                "velocity": 45.2,
                "acceleration": 3.1,
                "mentions": 1250,
                "viral_probability": 0.78,
                "engagement_rate": 4.2,
                "geographic_spread": 6,
                "platforms": [Platform.TWITTER, Platform.REDDIT],
                "stage": "growing",
                "created_at": datetime.now(timezone.utc)
            },
            {
                "keyword": "tech_trend",
                "trend_score": 0.92,
                "velocity": 67.8,
                "acceleration": 5.4,
                "mentions": 2100,
                "viral_probability": 0.89,
                "engagement_rate": 6.1,
                "geographic_spread": 8,
                "platforms": [Platform.TWITTER, Platform.YOUTUBE, Platform.INSTAGRAM],
                "stage": "peak",
                "created_at": datetime.now(timezone.utc)
            }
        ]
    
    async def _evaluate_trend_alerts(self, trend_data: Dict[str, Any]):
        """Evaluate a trend against all alert rules"""
        keyword = trend_data.get("keyword", "")
        
        for rule_id, rule in self.alert_rules.items():
            if not rule.enabled:
                continue
            
            # Check cooldown
            if await self._is_in_cooldown(rule_id, keyword):
                continue
            
            # Check if conditions are met
            if await self._check_rule_conditions(rule, trend_data):
                await self._trigger_alert(rule, trend_data)
    
    async def _check_rule_conditions(self, rule: AlertRule, trend_data: Dict[str, Any]) -> bool:
        """Check if trend data meets rule conditions"""
        try:
            for field, condition in rule.conditions.items():
                value = trend_data.get(field)
                
                if value is None:
                    continue
                
                # Check minimum value
                if "min" in condition and value < condition["min"]:
                    return False
                
                # Check maximum value
                if "max" in condition and value > condition["max"]:
                    return False
                
                # Check exact match
                if "equals" in condition and value != condition["equals"]:
                    return False
                
                # Check if value is in list
                if "in" in condition and value not in condition["in"]:
                    return False
            
            # Check platform filter
            if rule.platforms:
                trend_platforms = trend_data.get("platforms", [])
                if not any(platform in trend_platforms for platform in rule.platforms):
                    return False
            
            # Check keyword filter
            if rule.keywords:
                keyword = trend_data.get("keyword", "")
                if not any(kw in keyword.lower() for kw in rule.keywords):
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking rule conditions: {e}")
            return False
    
    async def _trigger_alert(self, rule: AlertRule, trend_data: Dict[str, Any]):
        """Trigger an alert based on rule and trend data"""
        try:
            keyword = trend_data.get("keyword", "")
            platforms = trend_data.get("platforms", [])
            
            # Create alert
            alert_id = f"{rule.id}_{keyword}_{int(datetime.now(timezone.utc).timestamp())}"
            
            # Generate alert message
            message = self._generate_alert_message(rule, trend_data)
            
            # Extract relevant metrics
            metrics = {
                "trend_score": trend_data.get("trend_score", 0),
                "velocity": trend_data.get("velocity", 0),
                "mentions": trend_data.get("mentions", 0),
                "viral_probability": trend_data.get("viral_probability", 0),
                "engagement_rate": trend_data.get("engagement_rate", 0)
            }
            
            # Create alert object
            alert = Alert(
                id=alert_id,
                type=rule.alert_type,
                severity=rule.severity,
                keyword=keyword,
                message=message,
                platforms=platforms,
                metrics=metrics,
                threshold_values=rule.conditions,
                created_at=get_current_timestamp(),
                expires_at=get_current_timestamp() + timedelta(hours=24),
                metadata={
                    "rule_id": rule.id,
                    "rule_name": rule.name,
                    "trend_data": trend_data
                }
            )
            
            # Store alert
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
            
            # Set cooldown
            await self._set_cooldown(rule.id, keyword, rule.cooldown_minutes)
            
            # Send notifications
            await self._send_notifications(alert)
            
            logger.info(f"Alert triggered: {alert.type.value} for '{keyword}' with severity {alert.severity.value}")
            
        except Exception as e:
            logger.error(f"Error triggering alert: {e}")
    
    def _generate_alert_message(self, rule: AlertRule, trend_data: Dict[str, Any]) -> str:
        """Generate human-readable alert message"""
        keyword = trend_data.get("keyword", "unknown")
        
        message_templates = {
            AlertType.TREND_EMERGENCE: f"New trend '{keyword}' detected with high emergence potential",
            AlertType.VELOCITY_CHANGE: f"Trend '{keyword}' showing significant velocity change",
            AlertType.VIRAL_PREDICTION: f"Trend '{keyword}' has high viral potential",
            AlertType.GEOGRAPHIC_SPREAD: f"Trend '{keyword}' spreading across multiple regions",
            AlertType.SATURATION_WARNING: f"Trend '{keyword}' showing signs of saturation"
        }
        
        base_message = message_templates.get(rule.alert_type, f"Alert for trend '{keyword}'")
        
        # Add key metrics to message
        metrics_info = []
        if "trend_score" in trend_data:
            metrics_info.append(f"score: {trend_data['trend_score']:.2f}")
        if "velocity" in trend_data:
            metrics_info.append(f"velocity: {trend_data['velocity']:.1f}")
        if "mentions" in trend_data:
            metrics_info.append(f"mentions: {trend_data['mentions']}")
        
        if metrics_info:
            base_message += f" ({', '.join(metrics_info)})"
        
        return base_message
    
    async def _is_in_cooldown(self, rule_id: str, keyword: str) -> bool:
        """Check if alert is in cooldown period"""
        cooldown_key = f"alert_cooldown:{rule_id}:{keyword}"
        return await self.cache_manager.exists(cooldown_key)
    
    async def _set_cooldown(self, rule_id: str, keyword: str, minutes: int):
        """Set cooldown period for alert"""
        cooldown_key = f"alert_cooldown:{rule_id}:{keyword}"
        await self.cache_manager.set(cooldown_key, "1", ttl=minutes * 60)
    
    async def _send_notifications(self, alert: Alert):
        """Send alert notifications"""
        try:
            # Send to all registered notification handlers
            for handler in self.notification_handlers:
                try:
                    await handler(alert)
                except Exception as e:
                    logger.error(f"Error in notification handler: {e}")
            
            # Default logging notification
            logger.warning(
                f"ALERT: {alert.severity.value.upper()} - {alert.type.value} - {alert.message}"
            )
            
        except Exception as e:
            logger.error(f"Error sending notifications: {e}")
    
    async def _cleanup_expired_alerts(self):
        """Remove expired alerts"""
        try:
            current_time = get_current_timestamp()
            expired_alerts = []
            
            for alert_id, alert in self.active_alerts.items():
                if alert.expires_at and current_time > alert.expires_at:
                    expired_alerts.append(alert_id)
            
            for alert_id in expired_alerts:
                del self.active_alerts[alert_id]
            
            if expired_alerts:
                logger.info(f"Cleaned up {len(expired_alerts)} expired alerts")
                
        except Exception as e:
            logger.error(f"Error cleaning up expired alerts: {e}")
    
    async def acknowledge_alert(self, alert_id: str, acknowledged_by: str = "system") -> bool:
        """Acknowledge an alert"""
        try:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.acknowledged = True
                alert.acknowledged_at = get_current_timestamp()
                alert.acknowledged_by = acknowledged_by
                
                logger.info(f"Alert {alert_id} acknowledged by {acknowledged_by}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error acknowledging alert {alert_id}: {e}")
            return False
    
    async def get_active_alerts(self, alert_type: Optional[AlertType] = None, 
                              severity: Optional[AlertSeverity] = None) -> List[Alert]:
        """Get active alerts with optional filtering"""
        alerts = list(self.active_alerts.values())
        
        if alert_type:
            alerts = [a for a in alerts if a.type == alert_type]
        
        if severity:
            alerts = [a for a in alerts if a.severity == severity]
        
        # Sort by creation time (newest first)
        alerts.sort(key=lambda x: x.created_at, reverse=True)
        
        return alerts
    
    async def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """Get alert history"""
        return self.alert_history[-limit:]
    
    def add_notification_handler(self, handler: Callable):
        """Add a notification handler"""
        self.notification_handlers.append(handler)
        logger.info("Notification handler added")
    
    def remove_notification_handler(self, handler: Callable):
        """Remove a notification handler"""
        if handler in self.notification_handlers:
            self.notification_handlers.remove(handler)
            logger.info("Notification handler removed")
    
    async def create_alert_rule(self, rule: AlertRule) -> bool:
        """Create a new alert rule"""
        try:
            self.alert_rules[rule.id] = rule
            await self._save_alert_rules()
            logger.info(f"Alert rule '{rule.name}' created")
            return True
            
        except Exception as e:
            logger.error(f"Error creating alert rule: {e}")
            return False
    
    async def update_alert_rule(self, rule_id: str, updates: Dict[str, Any]) -> bool:
        """Update an existing alert rule"""
        try:
            if rule_id not in self.alert_rules:
                return False
            
            rule = self.alert_rules[rule_id]
            for key, value in updates.items():
                if hasattr(rule, key):
                    setattr(rule, key, value)
            
            await self._save_alert_rules()
            logger.info(f"Alert rule '{rule_id}' updated")
            return True
            
        except Exception as e:
            logger.error(f"Error updating alert rule: {e}")
            return False
    
    async def delete_alert_rule(self, rule_id: str) -> bool:
        """Delete an alert rule"""
        try:
            if rule_id in self.alert_rules:
                del self.alert_rules[rule_id]
                await self._save_alert_rules()
                logger.info(f"Alert rule '{rule_id}' deleted")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error deleting alert rule: {e}")
            return False
    
    async def _load_alert_rules(self):
        """Load alert rules from storage"""
        # This would load from database in production
        pass
    
    async def _save_alert_rules(self):
        """Save alert rules to storage"""
        # This would save to database in production
        pass
    
    async def _load_active_alerts(self):
        """Load active alerts from storage"""
        # This would load from database in production
        pass
    
    def is_healthy(self) -> bool:
        """Check if alert manager is healthy"""
        return (
            self.is_monitoring and
            self.last_check_time is not None and
            (get_current_timestamp() - self.last_check_time).total_seconds() < 300  # 5 minutes
        )
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get alert system metrics"""
        return {
            "active_alerts": len(self.active_alerts),
            "total_rules": len(self.alert_rules),
            "enabled_rules": len([r for r in self.alert_rules.values() if r.enabled]),
            "alerts_by_severity": {
                severity.value: len([a for a in self.active_alerts.values() if a.severity == severity])
                for severity in AlertSeverity
            },
            "alerts_by_type": {
                alert_type.value: len([a for a in self.active_alerts.values() if a.type == alert_type])
                for alert_type in AlertType
            },
            "unacknowledged_alerts": len([a for a in self.active_alerts.values() if not a.acknowledged]),
            "is_monitoring": self.is_monitoring,
            "last_check": self.last_check_time.isoformat() if self.last_check_time else None
        }
