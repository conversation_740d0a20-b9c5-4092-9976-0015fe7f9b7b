"""
Database configuration and connection management for TrendRadar
"""

import asyncio
from typing import AsyncGenerator
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import redis.asyncio as redis
from config.settings import settings
import logging

logger = logging.getLogger(__name__)

# SQLAlchemy Base
Base = declarative_base()
metadata = MetaData()

# Database engines
engine = None
async_engine = None
SessionLocal = None
AsyncSessionLocal = None

# Redis connection
redis_client = None


def get_database_url(async_mode: bool = False) -> str:
    """Get database URL with appropriate driver for sync/async mode"""
    url = settings.database_url
    
    if async_mode:
        # Convert to async driver
        if url.startswith("postgresql://"):
            url = url.replace("postgresql://", "postgresql+asyncpg://", 1)
        elif url.startswith("mysql://"):
            url = url.replace("mysql://", "mysql+aiomysql://", 1)
        elif url.startswith("sqlite://"):
            url = url.replace("sqlite://", "sqlite+aiosqlite://", 1)
    
    return url


def init_database():
    """Initialize database connections"""
    global engine, async_engine, SessionLocal, AsyncSessionLocal
    
    try:
        # Synchronous engine
        engine = create_engine(
            get_database_url(async_mode=False),
            pool_pre_ping=True,
            pool_recycle=300,
            echo=settings.debug
        )
        
        # Asynchronous engine
        async_engine = create_async_engine(
            get_database_url(async_mode=True),
            pool_pre_ping=True,
            pool_recycle=300,
            echo=settings.debug
        )
        
        # Session factories
        SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=engine
        )
        
        AsyncSessionLocal = async_sessionmaker(
            async_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        logger.info("Database connections initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def init_redis():
    """Initialize Redis connection"""
    global redis_client
    
    try:
        redis_client = redis.from_url(
            settings.redis_url,
            encoding="utf-8",
            decode_responses=True,
            max_connections=20
        )
        
        # Test connection
        await redis_client.ping()
        logger.info("Redis connection initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize Redis: {e}")
        raise


def get_db():
    """Dependency to get synchronous database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get asynchronous database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def get_redis():
    """Dependency to get Redis client"""
    return redis_client


class DatabaseManager:
    """Database management utilities"""
    
    @staticmethod
    def create_tables():
        """Create all database tables"""
        try:
            Base.metadata.create_all(bind=engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create tables: {e}")
            raise
    
    @staticmethod
    def drop_tables():
        """Drop all database tables"""
        try:
            Base.metadata.drop_all(bind=engine)
            logger.info("Database tables dropped successfully")
        except Exception as e:
            logger.error(f"Failed to drop tables: {e}")
            raise
    
    @staticmethod
    async def check_connection():
        """Check database connection health"""
        try:
            async with AsyncSessionLocal() as session:
                await session.execute("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"Database connection check failed: {e}")
            return False
    
    @staticmethod
    async def check_redis_connection():
        """Check Redis connection health"""
        try:
            await redis_client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis connection check failed: {e}")
            return False


class CacheManager:
    """Redis cache management utilities"""
    
    @staticmethod
    async def get(key: str):
        """Get value from cache"""
        try:
            return await redis_client.get(key)
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None
    
    @staticmethod
    async def set(key: str, value: str, ttl: int = None):
        """Set value in cache with optional TTL"""
        try:
            if ttl:
                await redis_client.setex(key, ttl, value)
            else:
                await redis_client.set(key, value)
            return True
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    @staticmethod
    async def delete(key: str):
        """Delete key from cache"""
        try:
            await redis_client.delete(key)
            return True
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    @staticmethod
    async def exists(key: str):
        """Check if key exists in cache"""
        try:
            return await redis_client.exists(key)
        except Exception as e:
            logger.error(f"Cache exists error for key {key}: {e}")
            return False
    
    @staticmethod
    async def increment(key: str, amount: int = 1):
        """Increment counter in cache"""
        try:
            return await redis_client.incrby(key, amount)
        except Exception as e:
            logger.error(f"Cache increment error for key {key}: {e}")
            return None
    
    @staticmethod
    async def expire(key: str, ttl: int):
        """Set expiration for key"""
        try:
            await redis_client.expire(key, ttl)
            return True
        except Exception as e:
            logger.error(f"Cache expire error for key {key}: {e}")
            return False


# Connection pool configuration
DATABASE_CONFIG = {
    "pool_size": 20,
    "max_overflow": 30,
    "pool_timeout": 30,
    "pool_recycle": 3600,
    "pool_pre_ping": True
}

REDIS_CONFIG = {
    "max_connections": 50,
    "retry_on_timeout": True,
    "health_check_interval": 30,
    "socket_keepalive": True,
    "socket_keepalive_options": {}
}


async def startup_database():
    """Startup database connections"""
    init_database()
    await init_redis()
    
    # Verify connections
    db_healthy = await DatabaseManager.check_connection()
    redis_healthy = await DatabaseManager.check_redis_connection()
    
    if not db_healthy:
        raise Exception("Database connection failed during startup")
    
    if not redis_healthy:
        raise Exception("Redis connection failed during startup")
    
    logger.info("All database connections established successfully")


async def shutdown_database():
    """Shutdown database connections"""
    global redis_client, async_engine
    
    try:
        if redis_client:
            await redis_client.close()
            logger.info("Redis connection closed")
        
        if async_engine:
            await async_engine.dispose()
            logger.info("Database engine disposed")
            
    except Exception as e:
        logger.error(f"Error during database shutdown: {e}")


# Database utilities for migrations and maintenance
class DatabaseUtils:
    """Database utility functions"""
    
    @staticmethod
    def get_table_names():
        """Get all table names"""
        return engine.table_names()
    
    @staticmethod
    def backup_database(backup_path: str):
        """Create database backup"""
        # Implementation depends on database type
        pass
    
    @staticmethod
    def restore_database(backup_path: str):
        """Restore database from backup"""
        # Implementation depends on database type
        pass
    
    @staticmethod
    async def cleanup_old_data(days: int = 90):
        """Clean up old data based on retention policy"""
        try:
            async with AsyncSessionLocal() as session:
                # Implementation for cleaning old trends, alerts, etc.
                # This will be implemented when models are created
                pass
        except Exception as e:
            logger.error(f"Data cleanup failed: {e}")
            raise
