"""
Enhanced Rate Limiting System for TrendRadar
Comprehensive rate limiting with per-endpoint, API key, and platform-specific controls
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass
import json
import hashlib

from config.settings import settings
from config.database import CacheManager
from src.utils.constants import Platform

logger = logging.getLogger(__name__)


@dataclass
class RateLimitRule:
    """Rate limit rule configuration"""
    name: str
    max_requests: int
    window_seconds: int
    burst_limit: Optional[int] = None
    scope: str = 'global'  # global, user, api_key, endpoint, platform
    enabled: bool = True
    
    def __post_init__(self):
        if self.burst_limit is None:
            self.burst_limit = self.max_requests


@dataclass
class RateLimitStatus:
    """Current rate limit status"""
    allowed: bool
    remaining: int
    reset_time: datetime
    retry_after: Optional[int] = None
    rule_name: str = ''
    current_usage: int = 0


class TokenBucket:
    """Token bucket algorithm implementation"""
    
    def __init__(self, capacity: int, refill_rate: float, initial_tokens: Optional[int] = None):
        self.capacity = capacity
        self.refill_rate = refill_rate  # tokens per second
        self.tokens = initial_tokens if initial_tokens is not None else capacity
        self.last_refill = time.time()
        self._lock = asyncio.Lock()
    
    async def consume(self, tokens: int = 1) -> bool:
        """Try to consume tokens from bucket"""
        async with self._lock:
            await self._refill()
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    async def _refill(self):
        """Refill tokens based on time elapsed"""
        now = time.time()
        elapsed = now - self.last_refill
        
        if elapsed > 0:
            new_tokens = elapsed * self.refill_rate
            self.tokens = min(self.capacity, self.tokens + new_tokens)
            self.last_refill = now
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current bucket status"""
        async with self._lock:
            await self._refill()
            return {
                'tokens': self.tokens,
                'capacity': self.capacity,
                'refill_rate': self.refill_rate,
                'last_refill': self.last_refill
            }


class SlidingWindowCounter:
    """Sliding window rate limiter"""
    
    def __init__(self, max_requests: int, window_seconds: int):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = deque()
        self._lock = asyncio.Lock()
    
    async def is_allowed(self) -> Tuple[bool, int]:
        """Check if request is allowed and return remaining count"""
        async with self._lock:
            now = time.time()
            cutoff = now - self.window_seconds
            
            # Remove old requests
            while self.requests and self.requests[0] <= cutoff:
                self.requests.popleft()
            
            current_count = len(self.requests)
            
            if current_count < self.max_requests:
                self.requests.append(now)
                return True, self.max_requests - current_count - 1
            
            return False, 0
    
    async def get_reset_time(self) -> datetime:
        """Get when the window resets"""
        async with self._lock:
            if not self.requests:
                return datetime.now(timezone.utc)
            
            oldest_request = self.requests[0]
            reset_time = oldest_request + self.window_seconds
            return datetime.fromtimestamp(reset_time, tz=timezone.utc)


class EnhancedRateLimiter:
    """Enhanced rate limiter with multiple strategies and scopes"""
    
    def __init__(self):
        self.cache_manager = CacheManager()
        self.rules: Dict[str, RateLimitRule] = {}
        self.counters: Dict[str, SlidingWindowCounter] = {}
        self.buckets: Dict[str, TokenBucket] = {}
        self.request_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Initialize default rules
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """Initialize default rate limiting rules"""
        default_rules = [
            # Global API limits
            RateLimitRule(
                name='global_api',
                max_requests=10000,
                window_seconds=3600,
                scope='global'
            ),
            
            # Per-user limits
            RateLimitRule(
                name='user_hourly',
                max_requests=1000,
                window_seconds=3600,
                scope='user'
            ),
            RateLimitRule(
                name='user_daily',
                max_requests=10000,
                window_seconds=86400,
                scope='user'
            ),
            
            # Per-endpoint limits
            RateLimitRule(
                name='trends_endpoint',
                max_requests=100,
                window_seconds=300,  # 5 minutes
                scope='endpoint'
            ),
            RateLimitRule(
                name='predictions_endpoint',
                max_requests=50,
                window_seconds=300,
                scope='endpoint'
            ),
            RateLimitRule(
                name='search_endpoint',
                max_requests=200,
                window_seconds=3600,
                scope='endpoint'
            ),
            
            # Platform-specific limits (for data collection)
            RateLimitRule(
                name='twitter_api',
                max_requests=300,
                window_seconds=900,  # 15 minutes
                scope='platform'
            ),
            RateLimitRule(
                name='reddit_api',
                max_requests=60,
                window_seconds=60,  # 1 minute
                scope='platform'
            ),
            RateLimitRule(
                name='youtube_api',
                max_requests=100,
                window_seconds=100,
                scope='platform'
            ),
            RateLimitRule(
                name='instagram_api',
                max_requests=200,
                window_seconds=3600,
                scope='platform'
            ),
            
            # API key tiers
            RateLimitRule(
                name='free_tier',
                max_requests=100,
                window_seconds=3600,
                scope='api_key'
            ),
            RateLimitRule(
                name='pro_tier',
                max_requests=1000,
                window_seconds=3600,
                scope='api_key'
            ),
            RateLimitRule(
                name='enterprise_tier',
                max_requests=10000,
                window_seconds=3600,
                scope='api_key'
            )
        ]
        
        for rule in default_rules:
            self.rules[rule.name] = rule
            
        logger.info(f"Initialized {len(default_rules)} rate limiting rules")
    
    async def check_rate_limit(self, request_context: Dict[str, Any]) -> RateLimitStatus:
        """Check if request is within rate limits"""
        try:
            # Extract context information
            user_id = request_context.get('user_id')
            api_key = request_context.get('api_key')
            endpoint = request_context.get('endpoint')
            platform = request_context.get('platform')
            ip_address = request_context.get('ip_address')
            user_tier = request_context.get('user_tier', 'free')
            
            # Check applicable rules in order of priority
            checks = []
            
            # 1. Global limits
            if 'global_api' in self.rules:
                checks.append(('global_api', 'global', 'global'))
            
            # 2. User-specific limits
            if user_id:
                checks.extend([
                    ('user_hourly', 'user', user_id),
                    ('user_daily', 'user', user_id)
                ])
            
            # 3. API key tier limits
            if api_key:
                tier_rule = f"{user_tier}_tier"
                if tier_rule in self.rules:
                    checks.append((tier_rule, 'api_key', api_key))
            
            # 4. Endpoint-specific limits
            if endpoint:
                endpoint_rule = f"{endpoint}_endpoint"
                if endpoint_rule in self.rules:
                    checks.append((endpoint_rule, 'endpoint', f"{endpoint}:{user_id or ip_address}"))
            
            # 5. Platform-specific limits (for data collection)
            if platform:
                platform_rule = f"{platform}_api"
                if platform_rule in self.rules:
                    checks.append((platform_rule, 'platform', platform))
            
            # Check each applicable rule
            for rule_name, scope, identifier in checks:
                rule = self.rules.get(rule_name)
                if not rule or not rule.enabled:
                    continue
                
                status = await self._check_rule(rule, identifier)
                if not status.allowed:
                    status.rule_name = rule_name
                    return status
            
            # All checks passed
            return RateLimitStatus(
                allowed=True,
                remaining=1000,  # Default high value
                reset_time=datetime.now(timezone.utc) + timedelta(hours=1)
            )
            
        except Exception as e:
            logger.error(f"Error checking rate limit: {e}")
            # Fail open - allow request but log error
            return RateLimitStatus(
                allowed=True,
                remaining=0,
                reset_time=datetime.now(timezone.utc)
            )
    
    async def _check_rule(self, rule: RateLimitRule, identifier: str) -> RateLimitStatus:
        """Check a specific rate limiting rule"""
        cache_key = f"rate_limit:{rule.name}:{identifier}"
        
        try:
            # Get or create counter for this rule/identifier
            counter_key = f"{rule.name}:{identifier}"
            
            if counter_key not in self.counters:
                self.counters[counter_key] = SlidingWindowCounter(
                    rule.max_requests, rule.window_seconds
                )
            
            counter = self.counters[counter_key]
            allowed, remaining = await counter.is_allowed()
            reset_time = await counter.get_reset_time()
            
            # Calculate retry after if not allowed
            retry_after = None
            if not allowed:
                retry_after = int((reset_time - datetime.now(timezone.utc)).total_seconds())
            
            # Track request in history
            self.request_history[identifier].append({
                'timestamp': time.time(),
                'rule': rule.name,
                'allowed': allowed
            })
            
            return RateLimitStatus(
                allowed=allowed,
                remaining=remaining,
                reset_time=reset_time,
                retry_after=retry_after,
                rule_name=rule.name,
                current_usage=rule.max_requests - remaining
            )
            
        except Exception as e:
            logger.error(f"Error checking rule {rule.name}: {e}")
            # Fail open
            return RateLimitStatus(
                allowed=True,
                remaining=0,
                reset_time=datetime.now(timezone.utc)
            )
    
    async def get_rate_limit_headers(self, request_context: Dict[str, Any]) -> Dict[str, str]:
        """Get rate limit headers for HTTP response"""
        status = await self.check_rate_limit(request_context)
        
        headers = {
            'X-RateLimit-Limit': str(1000),  # Default
            'X-RateLimit-Remaining': str(status.remaining),
            'X-RateLimit-Reset': str(int(status.reset_time.timestamp())),
            'X-RateLimit-Reset-After': str(int((status.reset_time - datetime.now(timezone.utc)).total_seconds()))
        }
        
        if status.retry_after:
            headers['Retry-After'] = str(status.retry_after)
        
        if status.rule_name:
            headers['X-RateLimit-Rule'] = status.rule_name
        
        return headers
    
    async def record_request(self, request_context: Dict[str, Any], 
                           response_status: int, response_time: float):
        """Record request for analytics and adaptive limiting"""
        try:
            user_id = request_context.get('user_id', 'anonymous')
            endpoint = request_context.get('endpoint', 'unknown')
            
            record = {
                'timestamp': time.time(),
                'user_id': user_id,
                'endpoint': endpoint,
                'status': response_status,
                'response_time': response_time,
                'ip_address': request_context.get('ip_address'),
                'user_agent': request_context.get('user_agent')
            }
            
            # Store in cache for analytics
            cache_key = f"request_log:{int(time.time())}"
            await self.cache_manager.set(cache_key, json.dumps(record), ttl=86400)
            
        except Exception as e:
            logger.error(f"Error recording request: {e}")
    
    async def get_usage_stats(self, identifier: str, 
                            time_range_hours: int = 24) -> Dict[str, Any]:
        """Get usage statistics for an identifier"""
        try:
            history = self.request_history.get(identifier, deque())
            cutoff = time.time() - (time_range_hours * 3600)
            
            recent_requests = [req for req in history if req['timestamp'] > cutoff]
            
            if not recent_requests:
                return {
                    'total_requests': 0,
                    'allowed_requests': 0,
                    'blocked_requests': 0,
                    'success_rate': 1.0,
                    'rules_triggered': []
                }
            
            total = len(recent_requests)
            allowed = sum(1 for req in recent_requests if req['allowed'])
            blocked = total - allowed
            
            rules_triggered = list(set(req['rule'] for req in recent_requests if not req['allowed']))
            
            return {
                'total_requests': total,
                'allowed_requests': allowed,
                'blocked_requests': blocked,
                'success_rate': allowed / total if total > 0 else 1.0,
                'rules_triggered': rules_triggered,
                'time_range_hours': time_range_hours
            }
            
        except Exception as e:
            logger.error(f"Error getting usage stats: {e}")
            return {}
    
    def add_rule(self, rule: RateLimitRule):
        """Add a new rate limiting rule"""
        self.rules[rule.name] = rule
        logger.info(f"Added rate limiting rule: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """Remove a rate limiting rule"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            logger.info(f"Removed rate limiting rule: {rule_name}")
    
    def update_rule(self, rule_name: str, **kwargs):
        """Update an existing rate limiting rule"""
        if rule_name in self.rules:
            rule = self.rules[rule_name]
            for key, value in kwargs.items():
                if hasattr(rule, key):
                    setattr(rule, key, value)
            logger.info(f"Updated rate limiting rule: {rule_name}")
    
    async def reset_limits(self, identifier: str, rule_name: Optional[str] = None):
        """Reset rate limits for an identifier"""
        try:
            if rule_name:
                # Reset specific rule
                counter_key = f"{rule_name}:{identifier}"
                if counter_key in self.counters:
                    del self.counters[counter_key]
            else:
                # Reset all rules for identifier
                keys_to_remove = [key for key in self.counters.keys() if key.endswith(f":{identifier}")]
                for key in keys_to_remove:
                    del self.counters[key]
            
            logger.info(f"Reset rate limits for {identifier}")
            
        except Exception as e:
            logger.error(f"Error resetting limits: {e}")
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """Get system-wide rate limiting statistics"""
        try:
            total_rules = len(self.rules)
            active_rules = sum(1 for rule in self.rules.values() if rule.enabled)
            total_counters = len(self.counters)
            
            # Calculate recent activity
            recent_activity = 0
            for history in self.request_history.values():
                cutoff = time.time() - 3600  # Last hour
                recent_activity += sum(1 for req in history if req['timestamp'] > cutoff)
            
            return {
                'total_rules': total_rules,
                'active_rules': active_rules,
                'total_counters': total_counters,
                'recent_requests_hour': recent_activity,
                'rules': {name: {
                    'max_requests': rule.max_requests,
                    'window_seconds': rule.window_seconds,
                    'scope': rule.scope,
                    'enabled': rule.enabled
                } for name, rule in self.rules.items()}
            }
            
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            return {}


# Platform-specific rate limiters

class PlatformRateLimiter:
    """Platform-specific rate limiter for social media APIs"""
    
    def __init__(self, platform: Platform):
        self.platform = platform
        self.limiter = EnhancedRateLimiter()
        
        # Platform-specific configurations
        self.platform_configs = {
            Platform.TWITTER: {
                'requests_per_window': 300,
                'window_minutes': 15,
                'burst_limit': 100
            },
            Platform.REDDIT: {
                'requests_per_window': 60,
                'window_minutes': 1,
                'burst_limit': 10
            },
            Platform.YOUTUBE: {
                'requests_per_window': 100,
                'window_minutes': 100,
                'burst_limit': 50
            },
            Platform.INSTAGRAM: {
                'requests_per_window': 200,
                'window_minutes': 60,
                'burst_limit': 50
            }
        }
        
        self._setup_platform_rules()
    
    def _setup_platform_rules(self):
        """Setup platform-specific rate limiting rules"""
        config = self.platform_configs.get(self.platform)
        if not config:
            return
        
        rule = RateLimitRule(
            name=f"{self.platform.value}_api",
            max_requests=config['requests_per_window'],
            window_seconds=config['window_minutes'] * 60,
            burst_limit=config['burst_limit'],
            scope='platform'
        )
        
        self.limiter.add_rule(rule)
    
    async def is_allowed(self) -> bool:
        """Check if platform API request is allowed"""
        context = {
            'platform': self.platform.value,
            'endpoint': 'api_call'
        }
        
        status = await self.limiter.check_rate_limit(context)
        return status.allowed
    
    async def wait_if_needed(self) -> float:
        """Wait if rate limited and return wait time"""
        context = {
            'platform': self.platform.value,
            'endpoint': 'api_call'
        }
        
        status = await self.limiter.check_rate_limit(context)
        
        if not status.allowed and status.retry_after:
            logger.info(f"Rate limited for {self.platform.value}, waiting {status.retry_after}s")
            await asyncio.sleep(status.retry_after)
            return status.retry_after
        
        return 0.0


# Global rate limiter instance
global_rate_limiter = EnhancedRateLimiter()
