"""
Reddit Data Collector for TrendRadar
Implements Reddit API integration for trend data collection
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
import aiohttp
import json

from config.api_keys import get_reddit_auth
from src.data_collection.base_collector import BaseCollector, CollectedContent
from src.utils.helpers import extract_hashtags, extract_mentions, extract_urls, clean_text
from src.utils.constants import Platform, ContentType

logger = logging.getLogger(__name__)


class RedditCollector(BaseCollector):
    """Reddit API data collector"""
    
    def __init__(self, platform: Platform = Platform.REDDIT):
        super().__init__(platform)
        self.base_url = "https://www.reddit.com"
        self.oauth_url = "https://www.reddit.com/api/v1/access_token"
        self.access_token = None
        self.session = None
        self.popular_subreddits = [
            "all", "popular", "news", "worldnews", "technology", 
            "politics", "entertainment", "sports", "gaming", "music"
        ]
    
    async def initialize(self) -> bool:
        """Initialize Reddit API connection"""
        try:
            auth_config = get_reddit_auth()
            if not auth_config:
                logger.error("Reddit authentication not configured")
                return False
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            # Get OAuth token
            if not await self._get_access_token(auth_config):
                logger.error("Failed to get Reddit access token")
                return False
            
            # Test API connection
            test_url = f"{self.base_url}/r/test/hot.json"
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "User-Agent": auth_config["user_agent"]
            }
            
            async with self.session.get(test_url, headers=headers, params={"limit": 1}) as response:
                if response.status == 200:
                    logger.info("Reddit API connection successful")
                    return True
                else:
                    logger.error(f"Reddit API test failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to initialize Reddit collector: {e}")
            return False
    
    async def _get_access_token(self, auth_config: Dict[str, str]) -> bool:
        """Get OAuth access token from Reddit"""
        try:
            auth = aiohttp.BasicAuth(
                auth_config["client_id"],
                auth_config["client_secret"]
            )
            
            headers = {
                "User-Agent": auth_config["user_agent"]
            }
            
            data = {
                "grant_type": "client_credentials"
            }
            
            async with self.session.post(self.oauth_url, auth=auth, headers=headers, data=data) as response:
                if response.status == 200:
                    token_data = await response.json()
                    self.access_token = token_data.get("access_token")
                    return bool(self.access_token)
                else:
                    logger.error(f"Reddit OAuth failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Reddit OAuth error: {e}")
            return False
    
    async def collect_trending_topics(self, limit: int = 50) -> List[str]:
        """Collect trending topics from Reddit"""
        try:
            trending_topics = set()
            
            # Get hot posts from popular subreddits
            for subreddit in self.popular_subreddits[:5]:  # Limit to avoid rate limits
                url = f"{self.base_url}/r/{subreddit}/hot.json"
                params = {"limit": 25}
                
                result = await self.make_request(self._make_api_request, url, params)
                if result and "data" in result and "children" in result["data"]:
                    for post in result["data"]["children"]:
                        post_data = post.get("data", {})
                        title = post_data.get("title", "")
                        selftext = post_data.get("selftext", "")
                        
                        # Extract hashtags and keywords from title and text
                        text_content = f"{title} {selftext}"
                        hashtags = extract_hashtags(text_content)
                        trending_topics.update(hashtags)
                        
                        # Add subreddit as a topic
                        subreddit_name = post_data.get("subreddit", "")
                        if subreddit_name:
                            trending_topics.add(subreddit_name.lower())
            
            return list(trending_topics)[:limit]
            
        except Exception as e:
            logger.error(f"Error collecting Reddit trending topics: {e}")
            return []
    
    async def search_content(self, query: str, limit: int = 100) -> List[CollectedContent]:
        """Search for Reddit content based on query"""
        try:
            url = f"{self.base_url}/search.json"
            params = {
                "q": query,
                "sort": "hot",
                "limit": min(limit, 100),
                "type": "link"
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "data" not in result or "children" not in result["data"]:
                return []
            
            content_list = []
            for post in result["data"]["children"]:
                try:
                    content = self._parse_reddit_post(post.get("data", {}))
                    if content:
                        content_list.append(content)
                except Exception as e:
                    logger.error(f"Error parsing Reddit post: {e}")
                    continue
            
            return content_list
            
        except Exception as e:
            logger.error(f"Error searching Reddit content for '{query}': {e}")
            return []
    
    async def get_user_content(self, user_id: str, limit: int = 50) -> List[CollectedContent]:
        """Get recent posts from a specific Reddit user"""
        try:
            url = f"{self.base_url}/user/{user_id}/submitted.json"
            params = {
                "limit": min(limit, 100),
                "sort": "new"
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "data" not in result or "children" not in result["data"]:
                return []
            
            content_list = []
            for post in result["data"]["children"]:
                try:
                    content = self._parse_reddit_post(post.get("data", {}))
                    if content:
                        content_list.append(content)
                except Exception as e:
                    logger.error(f"Error parsing Reddit user post: {e}")
                    continue
            
            return content_list
            
        except Exception as e:
            logger.error(f"Error getting Reddit user content for {user_id}: {e}")
            return []
    
    async def get_content_details(self, content_id: str) -> Optional[CollectedContent]:
        """Get detailed information about a specific Reddit post"""
        try:
            # Reddit post IDs need to be in format: subreddit/comments/post_id
            url = f"{self.base_url}/comments/{content_id}.json"
            params = {"limit": 1}
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or not isinstance(result, list) or len(result) == 0:
                return None
            
            post_listing = result[0]
            if "data" not in post_listing or "children" not in post_listing["data"]:
                return None
            
            if post_listing["data"]["children"]:
                post_data = post_listing["data"]["children"][0].get("data", {})
                return self._parse_reddit_post(post_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting Reddit content details for {content_id}: {e}")
            return None
    
    async def get_subreddit_content(self, subreddit: str, sort: str = "hot", limit: int = 50) -> List[CollectedContent]:
        """Get content from a specific subreddit"""
        try:
            url = f"{self.base_url}/r/{subreddit}/{sort}.json"
            params = {"limit": min(limit, 100)}
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "data" not in result or "children" not in result["data"]:
                return []
            
            content_list = []
            for post in result["data"]["children"]:
                try:
                    content = self._parse_reddit_post(post.get("data", {}))
                    if content:
                        content_list.append(content)
                except Exception as e:
                    logger.error(f"Error parsing subreddit post: {e}")
                    continue
            
            return content_list
            
        except Exception as e:
            logger.error(f"Error getting subreddit content for r/{subreddit}: {e}")
            return []
    
    async def _make_api_request(self, url: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Make API request to Reddit"""
        if not self.session or not self.access_token:
            logger.error("Reddit session or token not initialized")
            return None
        
        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "User-Agent": "TrendRadar/1.0"
            }
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:
                    logger.warning("Reddit rate limit exceeded")
                    self.metrics.rate_limited_requests += 1
                    return None
                elif response.status == 401:
                    logger.warning("Reddit token expired, refreshing...")
                    # Token refresh would be implemented here
                    return None
                else:
                    logger.error(f"Reddit API error: {response.status} - {await response.text()}")
                    return None
                    
        except Exception as e:
            logger.error(f"Reddit API request failed: {e}")
            return None
    
    def _parse_reddit_post(self, post_data: Dict[str, Any]) -> Optional[CollectedContent]:
        """Parse Reddit API response into CollectedContent"""
        try:
            post_id = post_data.get("id")
            if not post_id:
                return None
            
            title = post_data.get("title", "")
            selftext = post_data.get("selftext", "")
            text = f"{title}\n{selftext}".strip()
            
            author = post_data.get("author", "unknown")
            subreddit = post_data.get("subreddit", "")
            
            # Parse timestamps
            created_utc = post_data.get("created_utc", 0)
            created_at = datetime.fromtimestamp(created_utc, tz=timezone.utc)
            
            # Extract engagement metrics
            engagement_metrics = {
                "upvotes": post_data.get("ups", 0),
                "downvotes": post_data.get("downs", 0),
                "score": post_data.get("score", 0),
                "comments": post_data.get("num_comments", 0),
                "awards": post_data.get("total_awards_received", 0)
            }
            
            # Extract text elements
            hashtags = extract_hashtags(text)
            mentions = extract_mentions(text)
            urls = extract_urls(text)
            
            # Add post URL if it's a link post
            post_url = post_data.get("url", "")
            if post_url and post_url not in urls:
                urls.append(post_url)
            
            # Determine content type
            content_type = ContentType.TEXT.value
            media_urls = []
            
            if post_data.get("is_video"):
                content_type = ContentType.VIDEO.value
            elif post_data.get("post_hint") == "image":
                content_type = ContentType.IMAGE.value
                if post_url:
                    media_urls.append(post_url)
            elif post_data.get("is_self") == False:
                content_type = ContentType.LINK.value
            
            return CollectedContent(
                id=post_id,
                platform=Platform.REDDIT,
                content_type=content_type,
                text=clean_text(text),
                author_id=author,
                author_username=author,
                created_at=created_at,
                engagement_metrics=engagement_metrics,
                hashtags=hashtags,
                mentions=mentions,
                urls=urls,
                location=subreddit,  # Use subreddit as location
                language="en",  # Reddit is primarily English
                media_urls=media_urls,
                parent_id=None,
                raw_data=post_data
            )
            
        except Exception as e:
            logger.error(f"Error parsing Reddit post data: {e}")
            return None
    
    async def close(self):
        """Close the HTTP session"""
        if self.session:
            await self.session.close()
    
    def __del__(self):
        """Cleanup on deletion"""
        if self.session and not self.session.closed:
            asyncio.create_task(self.session.close())
