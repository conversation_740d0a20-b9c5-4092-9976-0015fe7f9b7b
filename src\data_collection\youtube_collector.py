"""
YouTube Data Collector for TrendRadar
Implements YouTube Data API v3 integration for trend data collection
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
import aiohttp
import json

from config.api_keys import get_youtube_auth
from src.data_collection.base_collector import BaseCollector, CollectedContent
from src.utils.helpers import extract_hashtags, extract_mentions, extract_urls, clean_text
from src.utils.constants import Platform, ContentType

logger = logging.getLogger(__name__)


class YouTubeCollector(BaseCollector):
    """YouTube Data API v3 data collector"""
    
    def __init__(self, platform: Platform = Platform.YOUTUBE):
        super().__init__(platform)
        self.base_url = "https://www.googleapis.com/youtube/v3"
        self.api_key = None
        self.session = None
    
    async def initialize(self) -> bool:
        """Initialize YouTube API connection"""
        try:
            auth_config = get_youtube_auth()
            if not auth_config:
                logger.error("YouTube authentication not configured")
                return False
            
            self.api_key = auth_config["api_key"]
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            # Test API connection
            test_url = f"{self.base_url}/search"
            params = {
                "part": "snippet",
                "q": "test",
                "maxResults": 1,
                "key": self.api_key
            }
            
            async with self.session.get(test_url, params=params) as response:
                if response.status == 200:
                    logger.info("YouTube API connection successful")
                    return True
                else:
                    logger.error(f"YouTube API test failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to initialize YouTube collector: {e}")
            return False
    
    async def collect_trending_topics(self, limit: int = 50) -> List[str]:
        """Collect trending topics from YouTube"""
        try:
            # Get trending videos
            url = f"{self.base_url}/videos"
            params = {
                "part": "snippet",
                "chart": "mostPopular",
                "regionCode": "US",
                "maxResults": 50,
                "key": self.api_key
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "items" not in result:
                return []
            
            trending_topics = set()
            
            for video in result["items"]:
                snippet = video.get("snippet", {})
                title = snippet.get("title", "")
                description = snippet.get("description", "")
                tags = snippet.get("tags", [])
                
                # Extract hashtags from title and description
                text_content = f"{title} {description}"
                hashtags = extract_hashtags(text_content)
                trending_topics.update(hashtags)
                
                # Add tags as topics
                trending_topics.update([tag.lower() for tag in tags])
                
                # Add category as topic
                category_id = snippet.get("categoryId")
                if category_id:
                    trending_topics.add(f"category_{category_id}")
            
            return list(trending_topics)[:limit]
            
        except Exception as e:
            logger.error(f"Error collecting YouTube trending topics: {e}")
            return []
    
    async def search_content(self, query: str, limit: int = 100) -> List[CollectedContent]:
        """Search for YouTube content based on query"""
        try:
            url = f"{self.base_url}/search"
            params = {
                "part": "snippet",
                "q": query,
                "type": "video",
                "order": "relevance",
                "maxResults": min(limit, 50),  # API limit
                "publishedAfter": (datetime.now(timezone.utc) - timedelta(days=7)).isoformat(),
                "key": self.api_key
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "items" not in result:
                return []
            
            # Get video IDs for detailed stats
            video_ids = [item["id"]["videoId"] for item in result["items"] if "videoId" in item.get("id", {})]
            
            # Get video statistics
            stats_data = await self._get_video_statistics(video_ids)
            
            content_list = []
            for video in result["items"]:
                try:
                    video_id = video.get("id", {}).get("videoId")
                    if video_id:
                        content = self._parse_youtube_video(video, stats_data.get(video_id, {}))
                        if content:
                            content_list.append(content)
                except Exception as e:
                    logger.error(f"Error parsing YouTube video: {e}")
                    continue
            
            return content_list
            
        except Exception as e:
            logger.error(f"Error searching YouTube content for '{query}': {e}")
            return []
    
    async def get_user_content(self, user_id: str, limit: int = 50) -> List[CollectedContent]:
        """Get recent videos from a specific YouTube channel"""
        try:
            # First get channel uploads playlist
            channel_url = f"{self.base_url}/channels"
            channel_params = {
                "part": "contentDetails",
                "id": user_id,
                "key": self.api_key
            }
            
            channel_result = await self.make_request(self._make_api_request, channel_url, channel_params)
            if not channel_result or "items" not in channel_result or not channel_result["items"]:
                return []
            
            uploads_playlist_id = channel_result["items"][0]["contentDetails"]["relatedPlaylists"]["uploads"]
            
            # Get videos from uploads playlist
            playlist_url = f"{self.base_url}/playlistItems"
            playlist_params = {
                "part": "snippet",
                "playlistId": uploads_playlist_id,
                "maxResults": min(limit, 50),
                "key": self.api_key
            }
            
            result = await self.make_request(self._make_api_request, playlist_url, playlist_params)
            if not result or "items" not in result:
                return []
            
            # Get video IDs for detailed stats
            video_ids = [item["snippet"]["resourceId"]["videoId"] for item in result["items"]]
            stats_data = await self._get_video_statistics(video_ids)
            
            content_list = []
            for item in result["items"]:
                try:
                    video_id = item["snippet"]["resourceId"]["videoId"]
                    # Create video object structure similar to search results
                    video_obj = {
                        "id": {"videoId": video_id},
                        "snippet": item["snippet"]
                    }
                    content = self._parse_youtube_video(video_obj, stats_data.get(video_id, {}))
                    if content:
                        content_list.append(content)
                except Exception as e:
                    logger.error(f"Error parsing channel video: {e}")
                    continue
            
            return content_list
            
        except Exception as e:
            logger.error(f"Error getting YouTube channel content for {user_id}: {e}")
            return []
    
    async def get_content_details(self, content_id: str) -> Optional[CollectedContent]:
        """Get detailed information about a specific YouTube video"""
        try:
            url = f"{self.base_url}/videos"
            params = {
                "part": "snippet,statistics",
                "id": content_id,
                "key": self.api_key
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "items" not in result or not result["items"]:
                return None
            
            video_data = result["items"][0]
            stats_data = video_data.get("statistics", {})
            
            # Create video object structure
            video_obj = {
                "id": {"videoId": content_id},
                "snippet": video_data["snippet"]
            }
            
            return self._parse_youtube_video(video_obj, stats_data)
            
        except Exception as e:
            logger.error(f"Error getting YouTube content details for {content_id}: {e}")
            return None
    
    async def _get_video_statistics(self, video_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get statistics for multiple videos"""
        if not video_ids:
            return {}
        
        try:
            url = f"{self.base_url}/videos"
            params = {
                "part": "statistics",
                "id": ",".join(video_ids),
                "key": self.api_key
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "items" not in result:
                return {}
            
            stats_data = {}
            for video in result["items"]:
                video_id = video.get("id")
                if video_id:
                    stats_data[video_id] = video.get("statistics", {})
            
            return stats_data
            
        except Exception as e:
            logger.error(f"Error getting YouTube video statistics: {e}")
            return {}
    
    async def _make_api_request(self, url: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Make API request to YouTube"""
        if not self.session or not self.api_key:
            logger.error("YouTube session or API key not initialized")
            return None
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 403:
                    error_data = await response.json()
                    if "quotaExceeded" in str(error_data):
                        logger.warning("YouTube API quota exceeded")
                        self.metrics.rate_limited_requests += 1
                    else:
                        logger.error(f"YouTube API forbidden: {error_data}")
                    return None
                else:
                    logger.error(f"YouTube API error: {response.status} - {await response.text()}")
                    return None
                    
        except Exception as e:
            logger.error(f"YouTube API request failed: {e}")
            return None
    
    def _parse_youtube_video(self, video_data: Dict[str, Any], stats_data: Dict[str, Any]) -> Optional[CollectedContent]:
        """Parse YouTube API response into CollectedContent"""
        try:
            video_id = video_data.get("id", {}).get("videoId")
            if not video_id:
                return None
            
            snippet = video_data.get("snippet", {})
            title = snippet.get("title", "")
            description = snippet.get("description", "")
            text = f"{title}\n{description}".strip()
            
            channel_id = snippet.get("channelId", "")
            channel_title = snippet.get("channelTitle", "unknown")
            
            # Parse timestamps
            published_at = snippet.get("publishedAt", "")
            created_at = datetime.fromisoformat(published_at.replace('Z', '+00:00')) if published_at else datetime.now(timezone.utc)
            
            # Extract engagement metrics
            engagement_metrics = {
                "views": int(stats_data.get("viewCount", 0)),
                "likes": int(stats_data.get("likeCount", 0)),
                "comments": int(stats_data.get("commentCount", 0)),
                "favorites": int(stats_data.get("favoriteCount", 0))
            }
            
            # Extract text elements
            hashtags = extract_hashtags(text)
            mentions = extract_mentions(text)
            urls = extract_urls(text)
            
            # Add video URL
            video_url = f"https://www.youtube.com/watch?v={video_id}"
            urls.append(video_url)
            
            # Get thumbnail URL
            thumbnails = snippet.get("thumbnails", {})
            media_urls = []
            if "high" in thumbnails:
                media_urls.append(thumbnails["high"]["url"])
            elif "medium" in thumbnails:
                media_urls.append(thumbnails["medium"]["url"])
            elif "default" in thumbnails:
                media_urls.append(thumbnails["default"]["url"])
            
            return CollectedContent(
                id=video_id,
                platform=Platform.YOUTUBE,
                content_type=ContentType.VIDEO.value,
                text=clean_text(text),
                author_id=channel_id,
                author_username=channel_title,
                created_at=created_at,
                engagement_metrics=engagement_metrics,
                hashtags=hashtags,
                mentions=mentions,
                urls=urls,
                location=None,
                language=snippet.get("defaultLanguage", "en"),
                media_urls=media_urls,
                parent_id=None,
                raw_data=video_data
            )
            
        except Exception as e:
            logger.error(f"Error parsing YouTube video data: {e}")
            return None
    
    async def close(self):
        """Close the HTTP session"""
        if self.session:
            await self.session.close()
    
    def __del__(self):
        """Cleanup on deletion"""
        if self.session and not self.session.closed:
            asyncio.create_task(self.session.close())
