"""
Sentiment Analysis for TrendRadar
Advanced sentiment analysis using multiple approaches
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone
import statistics

logger = logging.getLogger(__name__)


class SentimentAnalyzer:
    """Multi-approach sentiment analysis system"""
    
    def __init__(self):
        # Lexicon-based sentiment dictionaries
        self.positive_words = {
            'amazing', 'awesome', 'excellent', 'fantastic', 'great', 'incredible',
            'love', 'wonderful', 'perfect', 'outstanding', 'brilliant', 'superb',
            'happy', 'joy', 'excited', 'thrilled', 'delighted', 'pleased',
            'good', 'nice', 'beautiful', 'cool', 'fun', 'interesting',
            'best', 'better', 'win', 'success', 'achieve', 'accomplish'
        }
        
        self.negative_words = {
            'awful', 'terrible', 'horrible', 'disgusting', 'hate', 'worst',
            'bad', 'poor', 'disappointing', 'frustrating', 'annoying', 'stupid',
            'sad', 'angry', 'mad', 'upset', 'depressed', 'worried',
            'fail', 'failure', 'lose', 'problem', 'issue', 'wrong',
            'broken', 'useless', 'worthless', 'pathetic', 'ridiculous'
        }
        
        # Intensifiers and negations
        self.intensifiers = {
            'very': 1.5, 'extremely': 2.0, 'incredibly': 2.0, 'absolutely': 1.8,
            'really': 1.3, 'quite': 1.2, 'pretty': 1.1, 'so': 1.4,
            'totally': 1.6, 'completely': 1.7, 'highly': 1.4
        }
        
        self.negations = {
            'not', 'no', 'never', 'nothing', 'nobody', 'nowhere',
            'neither', 'nor', 'none', 'hardly', 'scarcely', 'barely'
        }
        
        # Emoji sentiment mapping
        self.emoji_sentiment = {
            '😀': 0.8, '😃': 0.8, '😄': 0.9, '😁': 0.8, '😆': 0.7, '😅': 0.6,
            '🤣': 0.8, '😂': 0.8, '🙂': 0.5, '😉': 0.6, '😊': 0.7, '😇': 0.8,
            '🥰': 0.9, '😍': 0.9, '🤩': 0.8, '😘': 0.7, '😗': 0.6, '😚': 0.7,
            '😙': 0.6, '😋': 0.6, '😛': 0.5, '😜': 0.6, '🤪': 0.5, '😝': 0.5,
            '🤑': 0.4, '🤗': 0.7, '🤭': 0.3, '🤫': 0.2, '🤔': 0.1, '🤐': 0.0,
            '🤨': -0.2, '😐': 0.0, '😑': -0.1, '😶': 0.0, '😏': 0.2, '😒': -0.3,
            '🙄': -0.4, '😬': -0.2, '🤥': -0.3, '😔': -0.6, '😕': -0.4, '🙁': -0.5,
            '☹️': -0.6, '😣': -0.5, '😖': -0.6, '😫': -0.7, '😩': -0.6, '🥺': -0.4,
            '😢': -0.7, '😭': -0.8, '😤': -0.5, '😠': -0.7, '😡': -0.8, '🤬': -0.9,
            '🤯': -0.3, '😳': -0.2, '🥵': -0.3, '🥶': -0.3, '😱': -0.6, '😨': -0.6,
            '😰': -0.7, '😥': -0.5, '😓': -0.4, '🤗': 0.7, '🤔': 0.1, '🤭': 0.3,
            '🤫': 0.2, '🤥': -0.3, '😶': 0.0, '😐': 0.0, '😑': -0.1, '😬': -0.2,
            '🙄': -0.4, '😯': -0.1, '😦': -0.3, '😧': -0.4, '😮': -0.1, '😲': -0.2,
            '🥱': -0.2, '😴': 0.1, '🤤': 0.2, '😪': -0.3, '😵': -0.5, '🤐': 0.0,
            '🥴': -0.2, '🤢': -0.6, '🤮': -0.8, '🤧': -0.3, '😷': -0.2, '🤒': -0.4,
            '🤕': -0.5, '🤑': 0.4, '🤠': 0.5, '😎': 0.6, '🤓': 0.3, '🧐': 0.2,
            '❤️': 0.9, '🧡': 0.8, '💛': 0.8, '💚': 0.8, '💙': 0.8, '💜': 0.8,
            '🖤': 0.3, '🤍': 0.7, '🤎': 0.5, '💔': -0.8, '❣️': 0.8, '💕': 0.8,
            '💞': 0.8, '💓': 0.8, '💗': 0.8, '💖': 0.8, '💘': 0.8, '💝': 0.8,
            '👍': 0.6, '👎': -0.6, '👌': 0.5, '🤏': 0.2, '✌️': 0.4, '🤞': 0.3,
            '🤟': 0.5, '🤘': 0.4, '🤙': 0.4, '👈': 0.1, '👉': 0.1, '👆': 0.1,
            '🖕': -0.8, '👇': 0.1, '☝️': 0.2, '👏': 0.6, '🙌': 0.7, '👐': 0.4,
            '🤲': 0.3, '🤝': 0.5, '🙏': 0.4, '✍️': 0.2, '💪': 0.6, '🦾': 0.5,
            '🦿': 0.2, '🦵': 0.1, '🦶': 0.1, '👂': 0.1, '🦻': 0.1, '👃': 0.1,
            '🔥': 0.7, '💯': 0.8, '💢': -0.6, '💥': 0.3, '💫': 0.5, '💦': 0.2,
            '💨': 0.1, '🕳️': -0.3, '💣': -0.7, '💬': 0.2, '👁️‍🗨️': 0.1, '🗨️': 0.2,
            '🗯️': -0.3, '💭': 0.1, '💤': 0.1
        }
        
        # Platform-specific adjustments
        self.platform_adjustments = {
            'twitter': 1.0,
            'reddit': 0.9,  # Reddit tends to be more negative
            'youtube': 1.1,  # YouTube comments can be more positive
            'instagram': 1.2  # Instagram tends to be more positive
        }
    
    def analyze_sentiment(self, text: str, platform: str = 'twitter', 
                         context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Main sentiment analysis method"""
        try:
            if not text or len(text.strip()) == 0:
                return self._empty_sentiment_result()
            
            # Clean text for analysis
            cleaned_text = self._clean_text_for_sentiment(text)
            
            # Multiple sentiment approaches
            lexicon_score = self._lexicon_based_sentiment(cleaned_text)
            emoji_score = self._emoji_sentiment_analysis(text)
            pattern_score = self._pattern_based_sentiment(cleaned_text)
            
            # Combine scores with weights
            combined_score = self._combine_sentiment_scores(
                lexicon_score, emoji_score, pattern_score
            )
            
            # Apply platform adjustment
            platform_adj = self.platform_adjustments.get(platform, 1.0)
            adjusted_score = combined_score * platform_adj
            
            # Normalize to [-1, 1] range
            final_score = max(-1.0, min(1.0, adjusted_score))
            
            # Determine sentiment type
            sentiment_type = self._score_to_sentiment_type(final_score)
            
            # Calculate confidence
            confidence = self._calculate_confidence(
                text, lexicon_score, emoji_score, pattern_score
            )
            
            # Additional analysis
            emotions = self._detect_emotions(cleaned_text)
            subjectivity = self._calculate_subjectivity(cleaned_text)
            
            return {
                'sentiment_score': final_score,
                'sentiment_type': sentiment_type,
                'confidence': confidence,
                'emotions': emotions,
                'subjectivity': subjectivity,
                'component_scores': {
                    'lexicon': lexicon_score,
                    'emoji': emoji_score,
                    'pattern': pattern_score
                },
                'platform': platform,
                'analyzed_at': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return self._empty_sentiment_result()
    
    def _clean_text_for_sentiment(self, text: str) -> str:
        """Clean text specifically for sentiment analysis"""
        # Remove URLs but keep the rest
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove excessive punctuation but keep some for emphasis
        text = re.sub(r'[!]{3,}', '!!!', text)
        text = re.sub(r'[?]{3,}', '???', text)
        text = re.sub(r'[.]{3,}', '...', text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _lexicon_based_sentiment(self, text: str) -> float:
        """Lexicon-based sentiment analysis"""
        if not text:
            return 0.0
        
        words = re.findall(r'\b\w+\b', text.lower())
        if not words:
            return 0.0
        
        sentiment_score = 0.0
        word_count = 0
        
        i = 0
        while i < len(words):
            word = words[i]
            
            # Check for negation in previous 2 words
            negated = False
            if i > 0 and words[i-1] in self.negations:
                negated = True
            elif i > 1 and words[i-2] in self.negations:
                negated = True
            
            # Check for intensifier in previous word
            intensifier = 1.0
            if i > 0 and words[i-1] in self.intensifiers:
                intensifier = self.intensifiers[words[i-1]]
            
            # Calculate word sentiment
            word_sentiment = 0.0
            if word in self.positive_words:
                word_sentiment = 1.0
            elif word in self.negative_words:
                word_sentiment = -1.0
            
            # Apply intensifier and negation
            if word_sentiment != 0.0:
                word_sentiment *= intensifier
                if negated:
                    word_sentiment *= -0.8  # Negation reduces but doesn't completely flip
                
                sentiment_score += word_sentiment
                word_count += 1
            
            i += 1
        
        # Average sentiment
        return sentiment_score / max(word_count, 1)
    
    def _emoji_sentiment_analysis(self, text: str) -> float:
        """Analyze sentiment based on emojis"""
        if not text:
            return 0.0
        
        emoji_scores = []
        for char in text:
            if char in self.emoji_sentiment:
                emoji_scores.append(self.emoji_sentiment[char])
        
        if not emoji_scores:
            return 0.0
        
        return statistics.mean(emoji_scores)
    
    def _pattern_based_sentiment(self, text: str) -> float:
        """Pattern-based sentiment analysis"""
        if not text:
            return 0.0
        
        score = 0.0
        
        # Exclamation marks (positive emphasis)
        exclamation_count = text.count('!')
        if exclamation_count > 0:
            score += min(0.3, exclamation_count * 0.1)
        
        # Question marks (neutral to slightly negative)
        question_count = text.count('?')
        if question_count > 2:
            score -= 0.1
        
        # All caps (can be positive or negative, assume neutral)
        caps_ratio = sum(1 for c in text if c.isupper()) / max(len(text), 1)
        if caps_ratio > 0.5:
            score += 0.1  # Slight positive for enthusiasm
        
        # Repeated letters (enthusiasm)
        repeated_pattern = re.findall(r'(\w)\1{2,}', text.lower())
        if repeated_pattern:
            score += min(0.2, len(repeated_pattern) * 0.05)
        
        # Positive phrases
        positive_phrases = [
            'thank you', 'thanks', 'appreciate', 'grateful', 'blessed',
            'love it', 'love this', 'amazing', 'awesome', 'incredible'
        ]
        for phrase in positive_phrases:
            if phrase in text.lower():
                score += 0.3
        
        # Negative phrases
        negative_phrases = [
            'hate it', 'hate this', 'disgusting', 'terrible', 'awful',
            'worst ever', 'never again', 'disappointed', 'frustrated'
        ]
        for phrase in negative_phrases:
            if phrase in text.lower():
                score -= 0.4
        
        return max(-1.0, min(1.0, score))
    
    def _combine_sentiment_scores(self, lexicon: float, emoji: float, pattern: float) -> float:
        """Combine different sentiment scores with weights"""
        # Weights for different approaches
        lexicon_weight = 0.5
        emoji_weight = 0.3
        pattern_weight = 0.2
        
        # If no emoji sentiment, redistribute weight
        if emoji == 0.0:
            lexicon_weight = 0.7
            pattern_weight = 0.3
            emoji_weight = 0.0
        
        combined = (lexicon * lexicon_weight + 
                   emoji * emoji_weight + 
                   pattern * pattern_weight)
        
        return combined
    
    def _score_to_sentiment_type(self, score: float) -> str:
        """Convert numerical score to sentiment type"""
        if score > 0.1:
            return 'positive'
        elif score < -0.1:
            return 'negative'
        else:
            return 'neutral'
    
    def _calculate_confidence(self, text: str, lexicon: float, emoji: float, pattern: float) -> float:
        """Calculate confidence in sentiment analysis"""
        confidence = 0.5  # Base confidence
        
        # Text length factor
        text_length = len(text.split())
        if text_length > 5:
            confidence += 0.2
        elif text_length > 10:
            confidence += 0.3
        
        # Agreement between methods
        scores = [s for s in [lexicon, emoji, pattern] if s != 0.0]
        if len(scores) > 1:
            # Check if scores agree in direction
            positive_count = sum(1 for s in scores if s > 0.1)
            negative_count = sum(1 for s in scores if s < -0.1)
            
            if positive_count == len(scores) or negative_count == len(scores):
                confidence += 0.3  # All methods agree
            elif abs(positive_count - negative_count) <= 1:
                confidence += 0.1  # Partial agreement
        
        # Strong sentiment indicators
        if abs(max(scores, default=0)) > 0.5:
            confidence += 0.2
        
        return min(1.0, confidence)
    
    def _detect_emotions(self, text: str) -> Dict[str, float]:
        """Detect specific emotions in text"""
        emotions = {
            'joy': 0.0, 'anger': 0.0, 'sadness': 0.0, 
            'fear': 0.0, 'surprise': 0.0, 'disgust': 0.0
        }
        
        if not text:
            return emotions
        
        text_lower = text.lower()
        
        # Emotion keywords
        emotion_keywords = {
            'joy': ['happy', 'joy', 'excited', 'thrilled', 'delighted', 'cheerful', 'elated'],
            'anger': ['angry', 'mad', 'furious', 'rage', 'annoyed', 'frustrated', 'outraged'],
            'sadness': ['sad', 'depressed', 'disappointed', 'upset', 'crying', 'heartbroken', 'melancholy'],
            'fear': ['scared', 'afraid', 'worried', 'anxious', 'nervous', 'terrified', 'panic'],
            'surprise': ['surprised', 'shocked', 'amazed', 'astonished', 'unexpected', 'wow'],
            'disgust': ['disgusting', 'gross', 'awful', 'revolting', 'repulsive', 'nasty']
        }
        
        total_words = len(text_lower.split())
        
        for emotion, keywords in emotion_keywords.items():
            count = sum(1 for keyword in keywords if keyword in text_lower)
            emotions[emotion] = count / max(total_words, 1)
        
        return emotions
    
    def _calculate_subjectivity(self, text: str) -> float:
        """Calculate subjectivity score (0=objective, 1=subjective)"""
        if not text:
            return 0.0
        
        subjective_indicators = [
            'i think', 'i believe', 'in my opinion', 'personally', 'i feel',
            'seems like', 'appears to', 'probably', 'maybe', 'perhaps',
            'amazing', 'terrible', 'beautiful', 'ugly', 'wonderful', 'awful'
        ]
        
        text_lower = text.lower()
        subjective_count = sum(1 for indicator in subjective_indicators if indicator in text_lower)
        
        # Normalize by text length
        words = text_lower.split()
        subjectivity = subjective_count / max(len(words), 1)
        
        return min(1.0, subjectivity * 5)  # Scale up for better distribution
    
    def _empty_sentiment_result(self) -> Dict[str, Any]:
        """Return empty sentiment result for error cases"""
        return {
            'sentiment_score': 0.0,
            'sentiment_type': 'neutral',
            'confidence': 0.0,
            'emotions': {'joy': 0.0, 'anger': 0.0, 'sadness': 0.0, 'fear': 0.0, 'surprise': 0.0, 'disgust': 0.0},
            'subjectivity': 0.0,
            'component_scores': {'lexicon': 0.0, 'emoji': 0.0, 'pattern': 0.0},
            'platform': 'unknown',
            'analyzed_at': datetime.now(timezone.utc).isoformat()
        }
    
    def batch_analyze_sentiment(self, texts: List[str], platform: str = 'twitter') -> List[Dict[str, Any]]:
        """Analyze sentiment for multiple texts"""
        results = []
        
        for text in texts:
            try:
                result = self.analyze_sentiment(text, platform)
                results.append(result)
            except Exception as e:
                logger.error(f"Error analyzing sentiment for text: {e}")
                results.append(self._empty_sentiment_result())
        
        return results
    
    def get_sentiment_summary(self, sentiment_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get summary statistics for a batch of sentiment results"""
        if not sentiment_results:
            return {}
        
        scores = [r['sentiment_score'] for r in sentiment_results if 'sentiment_score' in r]
        confidences = [r['confidence'] for r in sentiment_results if 'confidence' in r]
        
        sentiment_counts = {'positive': 0, 'negative': 0, 'neutral': 0}
        for result in sentiment_results:
            sentiment_type = result.get('sentiment_type', 'neutral')
            sentiment_counts[sentiment_type] += 1
        
        return {
            'total_analyzed': len(sentiment_results),
            'average_sentiment': statistics.mean(scores) if scores else 0.0,
            'sentiment_std': statistics.stdev(scores) if len(scores) > 1 else 0.0,
            'average_confidence': statistics.mean(confidences) if confidences else 0.0,
            'sentiment_distribution': sentiment_counts,
            'sentiment_percentages': {
                k: (v / len(sentiment_results)) * 100 
                for k, v in sentiment_counts.items()
            } if sentiment_results else {}
        }
