"""
Data Preprocessor for TrendRadar
Cleans and normalizes social media content for analysis
"""

import re
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone
import unicodedata
import html

from src.utils.helpers import extract_hashtags, extract_mentions, extract_urls, clean_text
from src.utils.constants import Platform, ContentType, REGEX_PATTERNS
from src.data_collection.base_collector import CollectedContent

logger = logging.getLogger(__name__)


class ContentPreprocessor:
    """Preprocesses social media content for analysis"""
    
    def __init__(self):
        self.emoji_pattern = re.compile(REGEX_PATTERNS['emoji'])
        self.url_pattern = re.compile(REGEX_PATTERNS['url'])
        self.hashtag_pattern = re.compile(REGEX_PATTERNS['hashtag'])
        self.mention_pattern = re.compile(REGEX_PATTERNS['mention'])
        
        # Common spam indicators
        self.spam_patterns = [
            r'(?i)\b(buy now|click here|limited time|act fast|free money)\b',
            r'(?i)\b(viagra|casino|lottery|winner|congratulations)\b',
            r'(?i)\b(make money|work from home|get rich|easy money)\b',
            r'(?i)\b(follow for follow|f4f|follow back|followback)\b'
        ]
        self.spam_regex = [re.compile(pattern) for pattern in self.spam_patterns]
        
        # Bot detection patterns
        self.bot_patterns = [
            r'^[a-zA-Z]+\d{4,}$',  # username like user1234
            r'(?i)\b(bot|automated|auto)\b',
            r'^.{1,3}$',  # very short usernames
        ]
        self.bot_regex = [re.compile(pattern) for pattern in self.bot_patterns]
    
    def preprocess_content(self, content: CollectedContent) -> Dict[str, Any]:
        """Main preprocessing pipeline"""
        try:
            # Clean and normalize text
            cleaned_text = self._clean_text(content.text)
            
            # Extract elements
            hashtags = self._extract_and_normalize_hashtags(content.text)
            mentions = self._extract_and_normalize_mentions(content.text)
            urls = self._extract_and_clean_urls(content.text)
            
            # Detect language
            language = self._detect_language(cleaned_text)
            
            # Quality assessment
            quality_score = self._calculate_quality_score(content, cleaned_text)
            
            # Spam and bot detection
            is_spam = self._detect_spam(cleaned_text, content.author_username)
            is_bot = self._detect_bot(content.author_username, cleaned_text)
            
            # Content categorization
            category = self._categorize_content(cleaned_text, hashtags)
            
            # Extract keywords
            keywords = self._extract_keywords(cleaned_text)
            
            # Emotion detection (basic)
            emotions = self._detect_emotions(cleaned_text)
            
            return {
                'cleaned_text': cleaned_text,
                'hashtags': hashtags,
                'mentions': mentions,
                'urls': urls,
                'language': language,
                'quality_score': quality_score,
                'is_spam': is_spam,
                'is_bot': is_bot,
                'category': category,
                'keywords': keywords,
                'emotions': emotions,
                'word_count': len(cleaned_text.split()),
                'char_count': len(cleaned_text),
                'processed_at': datetime.now(timezone.utc)
            }
            
        except Exception as e:
            logger.error(f"Error preprocessing content {content.id}: {e}")
            return {
                'cleaned_text': content.text or '',
                'hashtags': content.hashtags or [],
                'mentions': content.mentions or [],
                'urls': content.urls or [],
                'language': 'unknown',
                'quality_score': 0.0,
                'is_spam': False,
                'is_bot': False,
                'category': 'unknown',
                'keywords': [],
                'emotions': {},
                'word_count': 0,
                'char_count': 0,
                'processed_at': datetime.now(timezone.utc)
            }
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content"""
        if not text:
            return ""
        
        # Decode HTML entities
        text = html.unescape(text)
        
        # Normalize Unicode
        text = unicodedata.normalize('NFKC', text)
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove control characters
        text = ''.join(char for char in text if unicodedata.category(char)[0] != 'C')
        
        # Clean up common social media artifacts
        text = re.sub(r'RT\s+@\w+:', '', text)  # Remove RT prefix
        text = re.sub(r'\bvia\s+@\w+', '', text)  # Remove via mentions
        
        return text.strip()
    
    def _extract_and_normalize_hashtags(self, text: str) -> List[str]:
        """Extract and normalize hashtags"""
        if not text:
            return []
        
        hashtags = extract_hashtags(text)
        
        # Normalize hashtags
        normalized = []
        for hashtag in hashtags:
            # Convert to lowercase
            hashtag = hashtag.lower()
            
            # Remove non-alphanumeric characters except underscores
            hashtag = re.sub(r'[^\w]', '', hashtag)
            
            # Skip very short or very long hashtags
            if 2 <= len(hashtag) <= 50:
                normalized.append(hashtag)
        
        return list(set(normalized))  # Remove duplicates
    
    def _extract_and_normalize_mentions(self, text: str) -> List[str]:
        """Extract and normalize mentions"""
        if not text:
            return []
        
        mentions = extract_mentions(text)
        
        # Normalize mentions
        normalized = []
        for mention in mentions:
            # Convert to lowercase
            mention = mention.lower()
            
            # Remove non-alphanumeric characters except underscores
            mention = re.sub(r'[^\w]', '', mention)
            
            # Skip very short mentions
            if len(mention) >= 2:
                normalized.append(mention)
        
        return list(set(normalized))  # Remove duplicates
    
    def _extract_and_clean_urls(self, text: str) -> List[str]:
        """Extract and clean URLs"""
        if not text:
            return []
        
        urls = extract_urls(text)
        
        # Clean and validate URLs
        cleaned_urls = []
        for url in urls:
            # Remove tracking parameters
            url = re.sub(r'[?&](utm_|fbclid|gclid|ref_)', '', url)
            
            # Validate URL format
            if re.match(r'https?://', url) and len(url) > 10:
                cleaned_urls.append(url)
        
        return list(set(cleaned_urls))  # Remove duplicates
    
    def _detect_language(self, text: str) -> str:
        """Simple language detection"""
        if not text or len(text) < 10:
            return 'unknown'
        
        # Simple heuristics for common languages
        if re.search(r'[а-яё]', text.lower()):
            return 'ru'
        elif re.search(r'[一-龯]', text):
            return 'zh'
        elif re.search(r'[ひらがなカタカナ]', text):
            return 'ja'
        elif re.search(r'[가-힣]', text):
            return 'ko'
        elif re.search(r'[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]', text.lower()):
            # European languages with accents
            if re.search(r'\b(el|la|los|las|un|una|de|del|en|con|por|para)\b', text.lower()):
                return 'es'
            elif re.search(r'\b(le|la|les|un|une|de|du|des|et|ou|avec|pour)\b', text.lower()):
                return 'fr'
            elif re.search(r'\b(der|die|das|ein|eine|und|oder|mit|für|von)\b', text.lower()):
                return 'de'
            else:
                return 'en'
        else:
            return 'en'  # Default to English
    
    def _calculate_quality_score(self, content: CollectedContent, cleaned_text: str) -> float:
        """Calculate content quality score"""
        score = 0.0
        
        # Text length factor
        text_length = len(cleaned_text)
        if 10 <= text_length <= 500:
            score += 0.3
        elif text_length > 500:
            score += 0.2
        
        # Engagement factor
        engagement = content.engagement_metrics or {}
        total_engagement = sum(engagement.values()) if engagement else 0
        if total_engagement > 0:
            score += min(0.3, total_engagement / 100)
        
        # Author credibility
        if hasattr(content, 'author_verified') and content.author_verified:
            score += 0.2
        
        # Content completeness
        if content.hashtags:
            score += 0.1
        if content.urls:
            score += 0.1
        
        return min(1.0, score)
    
    def _detect_spam(self, text: str, username: str) -> bool:
        """Detect spam content"""
        if not text:
            return False
        
        # Check against spam patterns
        for pattern in self.spam_regex:
            if pattern.search(text):
                return True
        
        # Check for excessive repetition
        words = text.lower().split()
        if len(words) > 5:
            unique_words = set(words)
            if len(unique_words) / len(words) < 0.3:  # Less than 30% unique words
                return True
        
        # Check for excessive capitalization
        if len(text) > 20:
            caps_ratio = sum(1 for c in text if c.isupper()) / len(text)
            if caps_ratio > 0.7:
                return True
        
        # Check for excessive punctuation
        punct_count = sum(1 for c in text if c in '!?.')
        if punct_count > len(text) * 0.2:
            return True
        
        return False
    
    def _detect_bot(self, username: str, text: str) -> bool:
        """Detect bot accounts"""
        if not username:
            return False
        
        # Check username patterns
        for pattern in self.bot_regex:
            if pattern.search(username):
                return True
        
        # Check for automated content patterns
        if text:
            # Very repetitive content
            if len(set(text.split())) < 3 and len(text.split()) > 5:
                return True
            
            # Common bot phrases
            bot_phrases = ['automated', 'bot', 'generated', 'scheduled']
            if any(phrase in text.lower() for phrase in bot_phrases):
                return True
        
        return False
    
    def _categorize_content(self, text: str, hashtags: List[str]) -> str:
        """Categorize content into broad categories"""
        if not text and not hashtags:
            return 'unknown'
        
        content_lower = text.lower() if text else ''
        hashtags_lower = [h.lower() for h in hashtags]
        
        # Technology
        tech_keywords = ['ai', 'tech', 'software', 'coding', 'programming', 'startup', 'innovation']
        if any(keyword in content_lower for keyword in tech_keywords) or \
           any(keyword in hashtags_lower for keyword in tech_keywords):
            return 'technology'
        
        # News/Politics
        news_keywords = ['news', 'breaking', 'politics', 'government', 'election', 'policy']
        if any(keyword in content_lower for keyword in news_keywords) or \
           any(keyword in hashtags_lower for keyword in news_keywords):
            return 'news'
        
        # Entertainment
        entertainment_keywords = ['movie', 'music', 'celebrity', 'entertainment', 'show', 'concert']
        if any(keyword in content_lower for keyword in entertainment_keywords) or \
           any(keyword in hashtags_lower for keyword in entertainment_keywords):
            return 'entertainment'
        
        # Sports
        sports_keywords = ['sports', 'football', 'basketball', 'soccer', 'game', 'team', 'player']
        if any(keyword in content_lower for keyword in sports_keywords) or \
           any(keyword in hashtags_lower for keyword in sports_keywords):
            return 'sports'
        
        # Business
        business_keywords = ['business', 'finance', 'market', 'economy', 'investment', 'money']
        if any(keyword in content_lower for keyword in business_keywords) or \
           any(keyword in hashtags_lower for keyword in business_keywords):
            return 'business'
        
        # Lifestyle
        lifestyle_keywords = ['lifestyle', 'fashion', 'food', 'travel', 'health', 'fitness']
        if any(keyword in content_lower for keyword in lifestyle_keywords) or \
           any(keyword in hashtags_lower for keyword in lifestyle_keywords):
            return 'lifestyle'
        
        return 'general'
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract important keywords from text"""
        if not text or len(text) < 10:
            return []
        
        # Simple keyword extraction
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        
        # Remove common stop words
        stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
            'those', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'may', 'might', 'must', 'can', 'shall', 'not', 'no', 'yes', 'all',
            'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such'
        }
        
        keywords = [word for word in words if word not in stop_words and len(word) > 3]
        
        # Count frequency and return top keywords
        from collections import Counter
        word_counts = Counter(keywords)
        
        return [word for word, count in word_counts.most_common(10)]
    
    def _detect_emotions(self, text: str) -> Dict[str, float]:
        """Basic emotion detection"""
        if not text:
            return {}
        
        text_lower = text.lower()
        
        # Simple emotion keywords
        emotion_keywords = {
            'joy': ['happy', 'joy', 'excited', 'amazing', 'wonderful', 'great', 'awesome', 'love'],
            'anger': ['angry', 'mad', 'furious', 'hate', 'annoyed', 'frustrated', 'outraged'],
            'sadness': ['sad', 'depressed', 'disappointed', 'upset', 'crying', 'heartbroken'],
            'fear': ['scared', 'afraid', 'worried', 'anxious', 'nervous', 'terrified'],
            'surprise': ['surprised', 'shocked', 'amazed', 'unexpected', 'wow', 'omg'],
            'disgust': ['disgusting', 'gross', 'awful', 'terrible', 'horrible', 'nasty']
        }
        
        emotions = {}
        total_words = len(text_lower.split())
        
        for emotion, keywords in emotion_keywords.items():
            count = sum(1 for keyword in keywords if keyword in text_lower)
            emotions[emotion] = count / max(total_words, 1)
        
        return emotions
    
    def batch_preprocess(self, content_list: List[CollectedContent]) -> List[Dict[str, Any]]:
        """Process multiple content items in batch"""
        results = []
        
        for content in content_list:
            try:
                processed = self.preprocess_content(content)
                processed['content_id'] = content.id
                results.append(processed)
            except Exception as e:
                logger.error(f"Error processing content {content.id}: {e}")
                continue
        
        logger.info(f"Processed {len(results)} out of {len(content_list)} content items")
        return results
