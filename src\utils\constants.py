"""
Constants and enumerations for TrendRadar
Centralized definition of application constants
"""

from enum import Enum
from typing import Dict, List


class Platform(Enum):
    """Social media platforms"""
    TWITTER = "twitter"
    REDDIT = "reddit"
    YOUTUBE = "youtube"
    INSTAGRAM = "instagram"
    TIKTOK = "tiktok"
    LINKEDIN = "linkedin"


class TrendStage(Enum):
    """Trend lifecycle stages"""
    EMERGING = "emerging"
    GROWING = "growing"
    PEAK = "peak"
    DECLINING = "declining"
    DEAD = "dead"


class AlertType(Enum):
    """Types of alerts"""
    TREND_EMERGENCE = "trend_emergence"
    VELOCITY_CHANGE = "velocity_change"
    GEOGRAPHIC_SPREAD = "geographic_spread"
    COMPETITOR_ACTIVITY = "competitor_activity"
    SATURATION_WARNING = "saturation_warning"
    VIRAL_PREDICTION = "viral_prediction"


class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ContentType(Enum):
    """Content format types"""
    TEXT = "text"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    LINK = "link"
    POLL = "poll"


class SentimentType(Enum):
    """Sentiment analysis results"""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    MIXED = "mixed"


class GeographicRegion(Enum):
    """Geographic regions for trend analysis"""
    NORTH_AMERICA = "north_america"
    SOUTH_AMERICA = "south_america"
    EUROPE = "europe"
    ASIA = "asia"
    AFRICA = "africa"
    OCEANIA = "oceania"


class DataCollectionStatus(Enum):
    """Data collection status"""
    ACTIVE = "active"
    PAUSED = "paused"
    ERROR = "error"
    RATE_LIMITED = "rate_limited"


# Platform-specific constants
PLATFORM_LIMITS = {
    Platform.TWITTER: {
        "max_text_length": 280,
        "rate_limit_window": 900,  # 15 minutes
        "rate_limit_requests": 300,
        "search_results_per_request": 100
    },
    Platform.REDDIT: {
        "max_text_length": 40000,
        "rate_limit_window": 60,  # 1 minute
        "rate_limit_requests": 60,
        "search_results_per_request": 25
    },
    Platform.YOUTUBE: {
        "max_text_length": 5000,
        "rate_limit_window": 86400,  # 24 hours
        "rate_limit_requests": 10000,
        "search_results_per_request": 50
    },
    Platform.INSTAGRAM: {
        "max_text_length": 2200,
        "rate_limit_window": 3600,  # 1 hour
        "rate_limit_requests": 200,
        "search_results_per_request": 20
    }
}

# Trend analysis thresholds
TREND_THRESHOLDS = {
    "min_mentions": 10,
    "viral_threshold": 0.7,
    "velocity_threshold": 5.0,  # mentions per hour
    "acceleration_threshold": 2.0,  # change in velocity per hour
    "geographic_spread_threshold": 3,  # minimum regions
    "engagement_rate_threshold": 2.0,  # percentage
    "correlation_threshold": 0.6
}

# Time windows for analysis
TIME_WINDOWS = {
    "velocity_minutes": 60,
    "acceleration_minutes": 180,
    "trend_analysis_minutes": 1440,  # 24 hours
    "prediction_horizon_hours": 24,
    "historical_lookback_days": 30
}

# Scoring weights
SCORING_WEIGHTS = {
    "viral_prediction": {
        "engagement_rate": 0.3,
        "share_velocity": 0.25,
        "influencer_adoption": 0.2,
        "cross_platform_spread": 0.15,
        "sentiment_momentum": 0.1
    },
    "trend_score": {
        "mentions": 0.2,
        "velocity": 0.25,
        "acceleration": 0.2,
        "engagement": 0.2,
        "geographic_spread": 0.15
    },
    "content_opportunity": {
        "trend_strength": 0.3,
        "competition_level": 0.25,
        "timing_score": 0.2,
        "audience_alignment": 0.15,
        "format_suitability": 0.1
    }
}

# Geographic mappings
COUNTRY_CODES = {
    "United States": "US",
    "Canada": "CA",
    "United Kingdom": "GB",
    "Germany": "DE",
    "France": "FR",
    "Japan": "JP",
    "Australia": "AU",
    "Brazil": "BR",
    "India": "IN",
    "China": "CN",
    "Russia": "RU",
    "Mexico": "MX",
    "Italy": "IT",
    "Spain": "ES",
    "South Korea": "KR"
}

TIMEZONE_MAPPING = {
    "US": "America/New_York",
    "CA": "America/Toronto",
    "GB": "Europe/London",
    "DE": "Europe/Berlin",
    "FR": "Europe/Paris",
    "JP": "Asia/Tokyo",
    "AU": "Australia/Sydney",
    "BR": "America/Sao_Paulo",
    "IN": "Asia/Kolkata",
    "CN": "Asia/Shanghai",
    "RU": "Europe/Moscow",
    "MX": "America/Mexico_City",
    "IT": "Europe/Rome",
    "ES": "Europe/Madrid",
    "KR": "Asia/Seoul"
}

# Language codes
SUPPORTED_LANGUAGES = {
    "en": "English",
    "es": "Spanish",
    "fr": "French",
    "de": "German",
    "it": "Italian",
    "pt": "Portuguese",
    "ru": "Russian",
    "ja": "Japanese",
    "ko": "Korean",
    "zh": "Chinese",
    "ar": "Arabic",
    "hi": "Hindi"
}

# Content format preferences by platform
PLATFORM_CONTENT_PREFERENCES = {
    Platform.TWITTER: [ContentType.TEXT, ContentType.IMAGE, ContentType.VIDEO],
    Platform.INSTAGRAM: [ContentType.IMAGE, ContentType.VIDEO, ContentType.TEXT],
    Platform.YOUTUBE: [ContentType.VIDEO, ContentType.TEXT],
    Platform.REDDIT: [ContentType.TEXT, ContentType.LINK, ContentType.IMAGE],
    Platform.TIKTOK: [ContentType.VIDEO, ContentType.AUDIO],
    Platform.LINKEDIN: [ContentType.TEXT, ContentType.LINK, ContentType.IMAGE]
}

# Optimal posting times by platform (UTC hours)
OPTIMAL_POSTING_TIMES = {
    Platform.TWITTER: [13, 16, 19, 22],  # 9AM, 12PM, 3PM, 6PM EST
    Platform.INSTAGRAM: [15, 17, 21, 23],  # 11AM, 1PM, 5PM, 7PM EST
    Platform.YOUTUBE: [18, 20, 24],  # 2PM, 4PM, 8PM EST
    Platform.REDDIT: [14, 18, 22, 1],  # 10AM, 2PM, 6PM, 9PM EST
    Platform.LINKEDIN: [13, 16, 18],  # 9AM, 12PM, 2PM EST
}

# Machine learning model configurations
ML_MODEL_CONFIGS = {
    "trend_prediction": {
        "model_type": "lstm",
        "sequence_length": 24,
        "hidden_units": 128,
        "dropout_rate": 0.2,
        "learning_rate": 0.001,
        "batch_size": 32,
        "epochs": 100
    },
    "viral_prediction": {
        "model_type": "random_forest",
        "n_estimators": 100,
        "max_depth": 10,
        "min_samples_split": 5,
        "min_samples_leaf": 2,
        "random_state": 42
    },
    "hashtag_prediction": {
        "model_type": "gradient_boosting",
        "n_estimators": 200,
        "learning_rate": 0.1,
        "max_depth": 6,
        "subsample": 0.8,
        "random_state": 42
    },
    "sentiment_analysis": {
        "model_type": "transformer",
        "model_name": "cardiffnlp/twitter-roberta-base-sentiment-latest",
        "max_length": 512,
        "batch_size": 16
    }
}

# API endpoint patterns
API_ENDPOINTS = {
    Platform.TWITTER: {
        "search": "/2/tweets/search/recent",
        "user_tweets": "/2/users/{user_id}/tweets",
        "trends": "/1.1/trends/place.json",
        "users": "/2/users/by/username/{username}"
    },
    Platform.REDDIT: {
        "subreddit": "/r/{subreddit}",
        "search": "/search",
        "hot": "/hot",
        "new": "/new",
        "top": "/top"
    },
    Platform.YOUTUBE: {
        "search": "/search",
        "videos": "/videos",
        "channels": "/channels",
        "comments": "/commentThreads"
    },
    Platform.INSTAGRAM: {
        "media": "/me/media",
        "user": "/me"
    }
}

# Error codes and messages
ERROR_CODES = {
    "RATE_LIMIT_EXCEEDED": {
        "code": "E001",
        "message": "API rate limit exceeded"
    },
    "INVALID_API_KEY": {
        "code": "E002",
        "message": "Invalid or missing API key"
    },
    "DATA_COLLECTION_ERROR": {
        "code": "E003",
        "message": "Error during data collection"
    },
    "TREND_ANALYSIS_ERROR": {
        "code": "E004",
        "message": "Error during trend analysis"
    },
    "DATABASE_ERROR": {
        "code": "E005",
        "message": "Database operation failed"
    },
    "CACHE_ERROR": {
        "code": "E006",
        "message": "Cache operation failed"
    },
    "PREDICTION_ERROR": {
        "code": "E007",
        "message": "Error during prediction"
    },
    "ALERT_ERROR": {
        "code": "E008",
        "message": "Error in alert system"
    }
}

# Default configuration values
DEFAULT_CONFIG = {
    "data_collection_interval": 300,  # 5 minutes
    "trend_analysis_interval": 900,  # 15 minutes
    "alert_check_interval": 60,  # 1 minute
    "cache_ttl": 3600,  # 1 hour
    "max_concurrent_requests": 10,
    "request_timeout": 30,  # seconds
    "retry_attempts": 3,
    "retry_delay": 1.0,  # seconds
    "batch_size": 100,
    "max_queue_size": 10000
}

# Regular expressions for text processing
REGEX_PATTERNS = {
    "hashtag": r"#(\w+)",
    "mention": r"@(\w+)",
    "url": r"http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+",
    "email": r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",
    "phone": r"\b\d{3}[-.]?\d{3}[-.]?\d{4}\b",
    "emoji": r"[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]+"
}

# Database table names
TABLE_NAMES = {
    "trends": "trends",
    "content": "content",
    "users": "users",
    "alerts": "alerts",
    "hashtags": "hashtags",
    "mentions": "mentions",
    "geographic_data": "geographic_data",
    "predictions": "predictions",
    "metrics": "metrics"
}

# Cache key prefixes
CACHE_KEYS = {
    "trend": "trend:",
    "user": "user:",
    "hashtag": "hashtag:",
    "prediction": "prediction:",
    "alert": "alert:",
    "metrics": "metrics:",
    "rate_limit": "rate_limit:",
    "session": "session:"
}

# HTTP status codes for API responses
HTTP_STATUS = {
    "OK": 200,
    "CREATED": 201,
    "BAD_REQUEST": 400,
    "UNAUTHORIZED": 401,
    "FORBIDDEN": 403,
    "NOT_FOUND": 404,
    "RATE_LIMITED": 429,
    "INTERNAL_ERROR": 500,
    "SERVICE_UNAVAILABLE": 503
}
