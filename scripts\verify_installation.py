#!/usr/bin/env python3
"""
Installation Verification Script for TrendRadar
Comprehensive verification of the TrendRadar installation and setup
"""

import asyncio
import logging
import sys
import os
import subprocess
import time
from pathlib import Path
from typing import Dict, Any, List, Tuple
import requests
import json
import importlib

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import click

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class InstallationVerifier:
    """Comprehensive installation verification"""
    
    def __init__(self):
        self.project_root = project_root
        self.verification_results = {}
        self.errors = []
        self.warnings = []
        
    def verify_python_environment(self) -> bool:
        """Verify Python environment and version"""
        logger.info("Verifying Python environment...")
        
        try:
            # Check Python version
            version = sys.version_info
            if version < (3, 8):
                self.errors.append(f"Python 3.8+ required, found {version.major}.{version.minor}")
                return False
            
            logger.info(f"✓ Python version: {version.major}.{version.minor}.{version.micro}")
            
            # Check virtual environment
            in_venv = (hasattr(sys, 'real_prefix') or 
                      (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix))
            
            if in_venv:
                logger.info("✓ Virtual environment detected")
            else:
                self.warnings.append("Virtual environment not detected")
            
            return True
            
        except Exception as e:
            self.errors.append(f"Python environment check failed: {e}")
            return False
    
    def verify_project_structure(self) -> bool:
        """Verify project directory structure"""
        logger.info("Verifying project structure...")
        
        required_files = [
            'README.md',
            'requirements.txt',
            'setup.py',
            '.env.example',
            'Dockerfile',
            'docker-compose.yml',
            'src/main.py',
            'src/__init__.py',
            'config/settings.py',
            'config/database.py',
            'scripts/setup_database.py'
        ]
        
        required_directories = [
            'src',
            'config',
            'scripts',
            'tests',
            'database/models',
            'src/data_collection',
            'src/predictive_analytics',
            'src/api',
            'src/utils'
        ]
        
        missing_files = []
        missing_dirs = []
        
        # Check files
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        # Check directories
        for dir_path in required_directories:
            if not (self.project_root / dir_path).is_dir():
                missing_dirs.append(dir_path)
        
        if missing_files:
            self.errors.extend([f"Missing file: {f}" for f in missing_files])
        
        if missing_dirs:
            self.errors.extend([f"Missing directory: {d}" for d in missing_dirs])
        
        if not missing_files and not missing_dirs:
            logger.info("✓ Project structure is complete")
            return True
        else:
            return False
    
    def verify_dependencies(self) -> bool:
        """Verify all required dependencies are installed"""
        logger.info("Verifying dependencies...")
        
        required_packages = [
            'fastapi',
            'uvicorn',
            'sqlalchemy',
            'asyncpg',
            'redis',
            'httpx',
            'pydantic',
            'python-dotenv',
            'click',
            'pytest',
            'numpy'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                importlib.import_module(package.replace('-', '_'))
                logger.info(f"✓ {package}")
            except ImportError:
                missing_packages.append(package)
                logger.error(f"✗ {package}")
        
        if missing_packages:
            self.errors.append(f"Missing packages: {', '.join(missing_packages)}")
            return False
        
        logger.info("✓ All required dependencies are installed")
        return True
    
    def verify_configuration(self) -> bool:
        """Verify configuration files and settings"""
        logger.info("Verifying configuration...")
        
        try:
            # Check if .env file exists
            env_file = self.project_root / '.env'
            env_example = self.project_root / '.env.example'
            
            if not env_file.exists():
                if env_example.exists():
                    self.warnings.append(".env file not found, but .env.example exists")
                else:
                    self.errors.append("Neither .env nor .env.example found")
                    return False
            
            # Try to import settings
            from config.settings import settings
            
            # Check critical settings
            if not settings.secret_key or settings.secret_key == 'your-super-secret-key-change-this-in-production':
                self.warnings.append("Default secret key detected - change in production")
            
            if not settings.database_url:
                self.errors.append("DATABASE_URL not configured")
                return False
            
            logger.info("✓ Configuration loaded successfully")
            return True
            
        except Exception as e:
            self.errors.append(f"Configuration verification failed: {e}")
            return False
    
    def verify_database_connection(self) -> bool:
        """Verify database connectivity"""
        logger.info("Verifying database connection...")
        
        try:
            from config.database import init_database, engine
            
            # Initialize database
            init_database()
            
            # Test connection
            with engine.connect() as connection:
                result = connection.execute("SELECT 1 as test")
                if result.fetchone():
                    logger.info("✓ Database connection successful")
                    return True
            
        except Exception as e:
            self.errors.append(f"Database connection failed: {e}")
            return False
    
    def verify_database_schema(self) -> bool:
        """Verify database schema is set up correctly"""
        logger.info("Verifying database schema...")
        
        try:
            from config.database import engine
            
            # Check if main tables exist
            required_tables = [
                'trends', 'content', 'hashtags', 'alerts', 
                'users', 'trend_predictions', 'geographic_data'
            ]
            
            with engine.connect() as connection:
                # Get list of tables
                result = connection.execute("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """)
                
                existing_tables = [row[0] for row in result]
                missing_tables = [table for table in required_tables if table not in existing_tables]
                
                if missing_tables:
                    self.warnings.append(f"Missing database tables: {', '.join(missing_tables)}")
                    self.warnings.append("Run: python scripts/setup_database.py")
                    return False
                
                logger.info("✓ Database schema is complete")
                return True
                
        except Exception as e:
            self.errors.append(f"Database schema verification failed: {e}")
            return False
    
    def verify_redis_connection(self) -> bool:
        """Verify Redis connectivity (optional)"""
        logger.info("Verifying Redis connection...")
        
        try:
            from config.settings import settings
            import redis
            
            if not settings.redis_url:
                self.warnings.append("Redis URL not configured - caching disabled")
                return True
            
            r = redis.from_url(settings.redis_url)
            r.ping()
            
            logger.info("✓ Redis connection successful")
            return True
            
        except ImportError:
            self.warnings.append("Redis not installed - caching disabled")
            return True
        except Exception as e:
            self.warnings.append(f"Redis connection failed: {e}")
            return True  # Redis is optional
    
    def verify_api_functionality(self) -> bool:
        """Verify API can start and respond"""
        logger.info("Verifying API functionality...")
        
        try:
            # Import main app
            from src.main import app
            
            # Test that app can be created
            if app:
                logger.info("✓ FastAPI app created successfully")
                return True
            else:
                self.errors.append("Failed to create FastAPI app")
                return False
                
        except Exception as e:
            self.errors.append(f"API functionality verification failed: {e}")
            return False
    
    def verify_data_collection_modules(self) -> bool:
        """Verify data collection modules can be imported"""
        logger.info("Verifying data collection modules...")
        
        modules_to_test = [
            'src.data_collection.base_collector',
            'src.data_collection.twitter_collector',
            'src.data_collection.reddit_collector',
            'src.data_collection.youtube_collector',
            'src.data_collection.instagram_collector'
        ]
        
        failed_modules = []
        
        for module in modules_to_test:
            try:
                importlib.import_module(module)
                logger.info(f"✓ {module}")
            except Exception as e:
                failed_modules.append(f"{module}: {e}")
                logger.error(f"✗ {module}: {e}")
        
        if failed_modules:
            self.errors.extend(failed_modules)
            return False
        
        logger.info("✓ All data collection modules imported successfully")
        return True
    
    def verify_analytics_modules(self) -> bool:
        """Verify analytics and prediction modules"""
        logger.info("Verifying analytics modules...")
        
        modules_to_test = [
            'src.predictive_analytics.trend_detector',
            'src.data_processing.preprocessor',
            'src.data_processing.sentiment_analyzer',
            'src.forecasting.predictor'
        ]
        
        failed_modules = []
        
        for module in modules_to_test:
            try:
                importlib.import_module(module)
                logger.info(f"✓ {module}")
            except Exception as e:
                failed_modules.append(f"{module}: {e}")
                logger.error(f"✗ {module}: {e}")
        
        if failed_modules:
            self.errors.extend(failed_modules)
            return False
        
        logger.info("✓ All analytics modules imported successfully")
        return True
    
    def verify_test_suite(self) -> bool:
        """Verify test suite can run"""
        logger.info("Verifying test suite...")
        
        try:
            # Check if pytest is available
            result = subprocess.run([
                sys.executable, '-m', 'pytest', '--version'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                logger.info("✓ pytest is available")
                
                # Try to collect tests
                result = subprocess.run([
                    sys.executable, '-m', 'pytest', '--collect-only', '-q'
                ], capture_output=True, text=True, cwd=self.project_root)
                
                if result.returncode == 0:
                    logger.info("✓ Test collection successful")
                    return True
                else:
                    self.warnings.append(f"Test collection failed: {result.stderr}")
                    return False
            else:
                self.errors.append("pytest not available")
                return False
                
        except Exception as e:
            self.errors.append(f"Test suite verification failed: {e}")
            return False
    
    def run_comprehensive_verification(self) -> Dict[str, bool]:
        """Run all verification checks"""
        logger.info("Starting comprehensive installation verification...")
        
        checks = [
            ('Python Environment', self.verify_python_environment),
            ('Project Structure', self.verify_project_structure),
            ('Dependencies', self.verify_dependencies),
            ('Configuration', self.verify_configuration),
            ('Database Connection', self.verify_database_connection),
            ('Database Schema', self.verify_database_schema),
            ('Redis Connection', self.verify_redis_connection),
            ('API Functionality', self.verify_api_functionality),
            ('Data Collection Modules', self.verify_data_collection_modules),
            ('Analytics Modules', self.verify_analytics_modules),
            ('Test Suite', self.verify_test_suite)
        ]
        
        results = {}
        
        for check_name, check_func in checks:
            try:
                logger.info(f"\n--- {check_name} ---")
                results[check_name] = check_func()
            except Exception as e:
                logger.error(f"Check '{check_name}' failed with exception: {e}")
                results[check_name] = False
                self.errors.append(f"{check_name}: {e}")
        
        return results
    
    def print_verification_report(self, results: Dict[str, bool]):
        """Print comprehensive verification report"""
        print("\n" + "="*80)
        print("TRENDRADAR INSTALLATION VERIFICATION REPORT")
        print("="*80)
        
        # Summary
        total_checks = len(results)
        passed_checks = sum(results.values())
        success_rate = (passed_checks / total_checks) * 100
        
        print(f"\nSUMMARY: {passed_checks}/{total_checks} checks passed ({success_rate:.1f}%)")
        
        # Detailed results
        print("\nDETAILED RESULTS:")
        for check_name, passed in results.items():
            status = "✓ PASS" if passed else "✗ FAIL"
            print(f"  {status} {check_name}")
        
        # Errors
        if self.errors:
            print(f"\nERRORS ({len(self.errors)}):")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
        
        # Warnings
        if self.warnings:
            print(f"\nWARNINGS ({len(self.warnings)}):")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
        
        # Recommendations
        print("\nRECOMMENDATIONS:")
        if success_rate == 100:
            print("  ✓ Installation is complete and ready for use!")
            print("  ✓ Run 'python scripts/start_development.py' to start the development server")
        elif success_rate >= 80:
            print("  ⚠ Installation is mostly complete with minor issues")
            print("  ⚠ Address warnings above for optimal performance")
        else:
            print("  ✗ Installation has significant issues that need to be resolved")
            print("  ✗ Address errors above before proceeding")
        
        print("\nNEXT STEPS:")
        print("  1. Fix any errors listed above")
        print("  2. Configure API keys in .env file")
        print("  3. Run: python scripts/start_development.py")
        print("  4. Visit: http://localhost:8000/docs")
        print("  5. Run tests: pytest tests/")
        
        print("="*80)
        
        return success_rate


@click.command()
@click.option('--fix-issues', is_flag=True, help='Attempt to automatically fix common issues')
@click.option('--verbose', is_flag=True, help='Enable verbose output')
def main(fix_issues, verbose):
    """Verify TrendRadar installation"""
    
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    verifier = InstallationVerifier()
    
    # Run verification
    results = verifier.run_comprehensive_verification()
    
    # Print report
    success_rate = verifier.print_verification_report(results)
    
    # Auto-fix if requested
    if fix_issues and success_rate < 100:
        logger.info("\nAttempting to fix common issues...")
        
        # Try to install missing dependencies
        if not results.get('Dependencies', True):
            logger.info("Installing missing dependencies...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        
        # Try to setup database if missing
        if not results.get('Database Schema', True):
            logger.info("Setting up database schema...")
            subprocess.run([sys.executable, 'scripts/setup_database.py'])
        
        # Create .env from example if missing
        env_file = verifier.project_root / '.env'
        env_example = verifier.project_root / '.env.example'
        if not env_file.exists() and env_example.exists():
            logger.info("Creating .env from .env.example...")
            env_file.write_text(env_example.read_text())
    
    # Exit with appropriate code
    if success_rate >= 80:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
