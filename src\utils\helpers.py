"""
Utility functions and helpers for TrendRadar
Common functionality used across the application
"""

import asyncio
import hashlib
import json
import logging
import re
import time
from datetime import datetime, timezone, timedelta
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse
import uuid

from config.settings import settings


def setup_logging():
    """Configure application logging"""
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/trendradar.log') if settings.environment != 'testing' else logging.NullHandler()
        ]
    )


def generate_uuid() -> str:
    """Generate a unique identifier"""
    return str(uuid.uuid4())


def generate_hash(data: str) -> str:
    """Generate SHA-256 hash of data"""
    return hashlib.sha256(data.encode()).hexdigest()


def get_current_timestamp() -> datetime:
    """Get current UTC timestamp"""
    return datetime.now(timezone.utc)


def timestamp_to_string(timestamp: datetime) -> str:
    """Convert timestamp to ISO string"""
    return timestamp.isoformat()


def string_to_timestamp(timestamp_str: str) -> datetime:
    """Convert ISO string to timestamp"""
    return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))


def calculate_time_difference(start: datetime, end: datetime) -> float:
    """Calculate time difference in seconds"""
    return (end - start).total_seconds()


def format_duration(seconds: float) -> str:
    """Format duration in human-readable format"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        return f"{seconds/60:.1f}m"
    else:
        return f"{seconds/3600:.1f}h"


def clean_text(text: str) -> str:
    """Clean and normalize text content"""
    if not text:
        return ""
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove URLs
    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
    
    # Remove mentions and hashtags for cleaning (keep original for analysis)
    # text = re.sub(r'[@#]\w+', '', text)
    
    return text.strip()


def extract_hashtags(text: str) -> List[str]:
    """Extract hashtags from text"""
    if not text:
        return []
    
    hashtags = re.findall(r'#(\w+)', text.lower())
    return list(set(hashtags))  # Remove duplicates


def extract_mentions(text: str) -> List[str]:
    """Extract mentions from text"""
    if not text:
        return []
    
    mentions = re.findall(r'@(\w+)', text.lower())
    return list(set(mentions))  # Remove duplicates


def extract_urls(text: str) -> List[str]:
    """Extract URLs from text"""
    if not text:
        return []
    
    urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', text)
    return urls


def normalize_hashtag(hashtag: str) -> str:
    """Normalize hashtag for consistent storage"""
    return hashtag.lower().strip('#')


def is_valid_url(url: str) -> bool:
    """Validate URL format"""
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except:
        return False


def truncate_text(text: str, max_length: int = 280) -> str:
    """Truncate text to specified length"""
    if len(text) <= max_length:
        return text
    
    return text[:max_length-3] + "..."


def calculate_engagement_rate(likes: int, shares: int, comments: int, followers: int) -> float:
    """Calculate engagement rate"""
    if followers == 0:
        return 0.0
    
    total_engagement = likes + shares + comments
    return (total_engagement / followers) * 100


def calculate_velocity(current_value: float, previous_value: float, time_interval: float) -> float:
    """Calculate velocity (rate of change)"""
    if time_interval == 0:
        return 0.0
    
    return (current_value - previous_value) / time_interval


def calculate_acceleration(current_velocity: float, previous_velocity: float, time_interval: float) -> float:
    """Calculate acceleration (rate of velocity change)"""
    if time_interval == 0:
        return 0.0
    
    return (current_velocity - previous_velocity) / time_interval


def weighted_average(values: List[float], weights: List[float]) -> float:
    """Calculate weighted average"""
    if not values or not weights or len(values) != len(weights):
        return 0.0
    
    weighted_sum = sum(v * w for v, w in zip(values, weights))
    weight_sum = sum(weights)
    
    return weighted_sum / weight_sum if weight_sum > 0 else 0.0


def normalize_score(score: float, min_val: float = 0.0, max_val: float = 1.0) -> float:
    """Normalize score to specified range"""
    return max(min_val, min(max_val, score))


def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate text similarity using simple word overlap"""
    if not text1 or not text2:
        return 0.0
    
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    return len(intersection) / len(union) if union else 0.0


def batch_process(items: List[Any], batch_size: int = 100):
    """Process items in batches"""
    for i in range(0, len(items), batch_size):
        yield items[i:i + batch_size]


async def retry_async(func, max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """Retry async function with exponential backoff"""
    for attempt in range(max_retries):
        try:
            return await func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            
            await asyncio.sleep(delay * (backoff ** attempt))


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """Safely parse JSON string"""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """Safely serialize object to JSON"""
    try:
        return json.dumps(obj, default=str)
    except (TypeError, ValueError):
        return default


def get_country_from_timezone(timezone_str: str) -> Optional[str]:
    """Extract country code from timezone string"""
    timezone_mapping = {
        'America/New_York': 'US',
        'America/Los_Angeles': 'US',
        'America/Chicago': 'US',
        'America/Toronto': 'CA',
        'Europe/London': 'GB',
        'Europe/Berlin': 'DE',
        'Europe/Paris': 'FR',
        'Asia/Tokyo': 'JP',
        'Asia/Shanghai': 'CN',
        'Asia/Kolkata': 'IN',
        'Australia/Sydney': 'AU',
        'America/Sao_Paulo': 'BR'
    }
    
    return timezone_mapping.get(timezone_str)


def calculate_trend_score(mentions: int, velocity: float, acceleration: float, 
                         engagement_rate: float, geographic_spread: int) -> float:
    """Calculate comprehensive trend score"""
    # Normalize components
    mention_score = min(mentions / 1000, 1.0)  # Cap at 1000 mentions
    velocity_score = min(abs(velocity) / 100, 1.0)  # Cap at 100 mentions/hour
    acceleration_score = min(abs(acceleration) / 10, 1.0)  # Cap at 10 mentions/hour²
    engagement_score = min(engagement_rate / 10, 1.0)  # Cap at 10%
    geo_score = min(geographic_spread / 10, 1.0)  # Cap at 10 regions
    
    # Weighted combination
    weights = [0.2, 0.25, 0.2, 0.2, 0.15]  # mentions, velocity, acceleration, engagement, geo
    scores = [mention_score, velocity_score, acceleration_score, engagement_score, geo_score]
    
    return weighted_average(scores, weights)


def detect_language(text: str) -> str:
    """Simple language detection (can be enhanced with proper library)"""
    # This is a placeholder - in production, use langdetect or similar
    if not text:
        return "unknown"
    
    # Simple heuristics for common languages
    if re.search(r'[а-яё]', text.lower()):
        return "ru"
    elif re.search(r'[一-龯]', text):
        return "zh"
    elif re.search(r'[ひらがなカタカナ]', text):
        return "ja"
    elif re.search(r'[가-힣]', text):
        return "ko"
    else:
        return "en"  # Default to English


def format_number(number: Union[int, float], precision: int = 1) -> str:
    """Format large numbers with K, M, B suffixes"""
    if number < 1000:
        return str(int(number))
    elif number < 1000000:
        return f"{number/1000:.{precision}f}K"
    elif number < 1000000000:
        return f"{number/1000000:.{precision}f}M"
    else:
        return f"{number/1000000000:.{precision}f}B"


def get_time_bucket(timestamp: datetime, bucket_size_minutes: int = 60) -> datetime:
    """Round timestamp to time bucket for aggregation"""
    minutes = (timestamp.minute // bucket_size_minutes) * bucket_size_minutes
    return timestamp.replace(minute=minutes, second=0, microsecond=0)


class RateLimiter:
    """Simple rate limiter implementation"""
    
    def __init__(self, max_requests: int, window_seconds: int):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = []
    
    def is_allowed(self) -> bool:
        """Check if request is allowed"""
        now = time.time()
        
        # Remove old requests outside the window
        self.requests = [req_time for req_time in self.requests 
                        if now - req_time < self.window_seconds]
        
        # Check if we can make another request
        if len(self.requests) < self.max_requests:
            self.requests.append(now)
            return True
        
        return False
    
    def time_until_reset(self) -> float:
        """Get time until rate limit resets"""
        if not self.requests:
            return 0.0
        
        oldest_request = min(self.requests)
        return max(0.0, self.window_seconds - (time.time() - oldest_request))


class CircularBuffer:
    """Circular buffer for storing recent values"""
    
    def __init__(self, size: int):
        self.size = size
        self.buffer = []
        self.index = 0
    
    def add(self, value: Any):
        """Add value to buffer"""
        if len(self.buffer) < self.size:
            self.buffer.append(value)
        else:
            self.buffer[self.index] = value
            self.index = (self.index + 1) % self.size
    
    def get_all(self) -> List[Any]:
        """Get all values in chronological order"""
        if len(self.buffer) < self.size:
            return self.buffer.copy()
        
        return self.buffer[self.index:] + self.buffer[:self.index]
    
    def get_recent(self, count: int) -> List[Any]:
        """Get most recent values"""
        all_values = self.get_all()
        return all_values[-count:] if count <= len(all_values) else all_values
    
    def is_full(self) -> bool:
        """Check if buffer is full"""
        return len(self.buffer) == self.size
    
    def clear(self):
        """Clear buffer"""
        self.buffer.clear()
        self.index = 0
