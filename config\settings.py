"""
TrendRadar Configuration Settings
Centralized configuration management for the application
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application
    app_name: str = "TrendRadar"
    app_version: str = "1.0.0"
    debug: bool = False
    environment: str = "development"
    secret_key: str
    log_level: str = "INFO"
    
    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_workers: int = 4
    
    # Database
    database_url: str
    redis_url: str = "redis://localhost:6379/0"
    
    # Social Media APIs
    twitter_bearer_token: Optional[str] = None
    twitter_api_key: Optional[str] = None
    twitter_api_secret: Optional[str] = None
    twitter_access_token: Optional[str] = None
    twitter_access_token_secret: Optional[str] = None
    
    reddit_client_id: Optional[str] = None
    reddit_client_secret: Optional[str] = None
    reddit_user_agent: str = "TrendRadar/1.0"
    
    youtube_api_key: Optional[str] = None
    
    instagram_access_token: Optional[str] = None
    instagram_client_id: Optional[str] = None
    instagram_client_secret: Optional[str] = None
    
    # Optional APIs
    tiktok_access_token: Optional[str] = None
    linkedin_access_token: Optional[str] = None
    
    # Caching
    cache_ttl: int = 3600
    cache_max_size: int = 10000
    
    # Rate Limiting
    rate_limit_requests: int = 1000
    rate_limit_window: int = 3600
    
    # Monitoring
    enable_metrics: bool = True
    metrics_port: int = 9090
    
    # Email Configuration
    smtp_host: Optional[str] = None
    smtp_port: int = 587
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    from_email: Optional[str] = None
    
    # Webhooks
    webhook_secret: Optional[str] = None
    webhook_url: Optional[str] = None
    
    # Machine Learning
    ml_model_path: str = "models/"
    enable_gpu: bool = False
    batch_size: int = 32
    max_sequence_length: int = 512
    
    # Geographic
    default_timezone: str = "UTC"
    enable_geolocation: bool = True
    
    # Alerts
    max_alerts_per_user: int = 100
    alert_cooldown_minutes: int = 5
    
    # Data Retention
    data_retention_days: int = 90
    cleanup_interval_hours: int = 24
    
    # Security
    jwt_algorithm: str = "HS256"
    jwt_expiration_hours: int = 24
    password_min_length: int = 8
    
    # Frontend
    frontend_url: str = "http://localhost:3000"
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:8080"]
    
    # Development
    enable_swagger: bool = True
    enable_redoc: bool = True
    enable_debug_toolbar: bool = False
    
    @validator('cors_origins', pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator('database_url', pre=True)
    def validate_database_url(cls, v):
        if not v:
            raise ValueError("DATABASE_URL is required")
        return v
    
    @validator('secret_key', pre=True)
    def validate_secret_key(cls, v):
        if not v or len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = False


class DevelopmentSettings(Settings):
    """Development environment settings"""
    debug: bool = True
    log_level: str = "DEBUG"
    enable_debug_toolbar: bool = True


class ProductionSettings(Settings):
    """Production environment settings"""
    debug: bool = False
    log_level: str = "WARNING"
    enable_swagger: bool = False
    enable_redoc: bool = False
    enable_debug_toolbar: bool = False


class TestingSettings(Settings):
    """Testing environment settings"""
    debug: bool = True
    log_level: str = "DEBUG"
    database_url: str = "sqlite:///./test.db"
    redis_url: str = "redis://localhost:6379/1"


@lru_cache()
def get_settings() -> Settings:
    """Get application settings based on environment"""
    environment = os.getenv("ENVIRONMENT", "development").lower()
    
    if environment == "production":
        return ProductionSettings()
    elif environment == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()


# Global settings instance
settings = get_settings()


# API Rate Limits Configuration
RATE_LIMITS = {
    "twitter": {
        "requests_per_window": 300,
        "window_minutes": 15,
        "burst_limit": 50
    },
    "reddit": {
        "requests_per_window": 60,
        "window_minutes": 1,
        "burst_limit": 10
    },
    "youtube": {
        "requests_per_window": 10000,
        "window_minutes": 1440,  # Daily limit
        "burst_limit": 100
    },
    "instagram": {
        "requests_per_window": 200,
        "window_minutes": 60,
        "burst_limit": 20
    }
}

# Trend Analysis Configuration
TREND_CONFIG = {
    "min_mentions_threshold": 10,
    "velocity_window_minutes": 60,
    "acceleration_window_minutes": 180,
    "viral_threshold_score": 0.7,
    "geographic_spread_threshold": 3,  # minimum number of regions
    "trend_lifecycle_stages": ["emerging", "growing", "peak", "declining", "dead"],
    "correlation_threshold": 0.6
}

# Machine Learning Model Configuration
ML_CONFIG = {
    "trend_prediction": {
        "model_type": "lstm",
        "sequence_length": 24,  # hours
        "features": ["mentions", "engagement", "sentiment", "velocity"],
        "retrain_interval_hours": 168  # weekly
    },
    "viral_prediction": {
        "model_type": "random_forest",
        "features": ["engagement_rate", "share_velocity", "influencer_adoption", 
                    "cross_platform_spread", "sentiment_momentum"],
        "threshold": 0.8
    },
    "hashtag_prediction": {
        "model_type": "gradient_boosting",
        "lookback_hours": 72,
        "prediction_horizon_hours": 24
    }
}

# Geographic Analysis Configuration
GEO_CONFIG = {
    "supported_regions": [
        "North America", "South America", "Europe", "Asia", "Africa", "Oceania"
    ],
    "supported_countries": [
        "US", "CA", "GB", "DE", "FR", "JP", "AU", "BR", "IN", "CN"
    ],
    "timezone_mapping": {
        "US": "America/New_York",
        "CA": "America/Toronto",
        "GB": "Europe/London",
        "DE": "Europe/Berlin",
        "FR": "Europe/Paris",
        "JP": "Asia/Tokyo",
        "AU": "Australia/Sydney",
        "BR": "America/Sao_Paulo",
        "IN": "Asia/Kolkata"
    }
}

# Content Strategy Configuration
CONTENT_CONFIG = {
    "optimal_posting_times": {
        "twitter": ["09:00", "12:00", "15:00", "18:00"],
        "instagram": ["11:00", "13:00", "17:00", "19:00"],
        "youtube": ["14:00", "16:00", "20:00"],
        "reddit": ["10:00", "14:00", "18:00", "21:00"]
    },
    "content_formats": ["text", "image", "video", "audio", "link"],
    "engagement_weights": {
        "likes": 1.0,
        "shares": 3.0,
        "comments": 2.0,
        "saves": 2.5,
        "clicks": 1.5
    }
}

# Alert Configuration
ALERT_CONFIG = {
    "severity_levels": ["low", "medium", "high", "critical"],
    "notification_channels": ["email", "webhook", "dashboard"],
    "alert_types": [
        "trend_emergence",
        "velocity_change",
        "geographic_spread",
        "competitor_activity",
        "saturation_warning"
    ],
    "cooldown_periods": {
        "trend_emergence": 300,  # 5 minutes
        "velocity_change": 600,  # 10 minutes
        "geographic_spread": 900,  # 15 minutes
        "competitor_activity": 1800,  # 30 minutes
        "saturation_warning": 3600  # 1 hour
    }
}
