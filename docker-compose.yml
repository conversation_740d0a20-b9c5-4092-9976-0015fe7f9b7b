version: '3.8'

services:
  # TrendRadar Application
  trendradar:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: trendradar-app
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=***************************************************/trendradar
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./models:/app/models
    restart: unless-stopped
    networks:
      - trendradar-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: trendradar-postgres
    environment:
      POSTGRES_DB: trendradar
      POSTGRES_USER: trendradar
      POSTGRES_PASSWORD: trendradar123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - trendradar-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trendradar -d trendradar"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: trendradar-redis
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - trendradar-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: trendradar-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./frontend/build:/usr/share/nginx/html:ro
    depends_on:
      - trendradar
    restart: unless-stopped
    networks:
      - trendradar-network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: trendradar-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    restart: unless-stopped
    networks:
      - trendradar-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: trendradar-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - trendradar-network

  # Celery Worker (for background tasks)
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: trendradar-celery
    command: celery -A src.main worker --loglevel=info --concurrency=4
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=***************************************************/trendradar
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./models:/app/models
    restart: unless-stopped
    networks:
      - trendradar-network

  # Celery Beat (for scheduled tasks)
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: trendradar-celery-beat
    command: celery -A src.main beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=***************************************************/trendradar
      - REDIS_URL=redis://redis:6379/0
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - trendradar-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  trendradar-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
