"""
Test configuration and fixtures for TrendRadar
"""

import pytest
import asyncio
import os
import tempfile
from datetime import datetime, timezone
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock

# Set test environment
os.environ['ENVIRONMENT'] = 'test'
os.environ['DATABASE_URL'] = 'sqlite:///:memory:'
os.environ['REDIS_URL'] = 'redis://localhost:6379/15'  # Test database

from config.database import Base, engine, get_async_db
from src.data_collection.base_collector import CollectedContent
from src.utils.constants import Platform, ContentType


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
async def db_session():
    """Create a test database session"""
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    # Get session
    async for session in get_async_db():
        yield session
        break
    
    # Clean up
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def sample_content_data():
    """Sample content data for testing"""
    return {
        'id': 'test_content_123',
        'platform': Platform.TWITTER,
        'content_type': ContentType.TEXT.value,
        'text': 'This is a test tweet about #AI and #technology trends',
        'author_id': 'test_user_456',
        'author_username': 'testuser',
        'created_at': datetime.now(timezone.utc),
        'engagement_metrics': {
            'likes': 100,
            'retweets': 25,
            'comments': 10
        },
        'hashtags': ['ai', 'technology'],
        'mentions': ['@trendradar'],
        'urls': ['https://example.com'],
        'location': 'San Francisco, CA',
        'language': 'en',
        'sentiment_score': 0.7,
        'raw_data': {'original': 'data'}
    }


@pytest.fixture
def sample_collected_content(sample_content_data):
    """Sample CollectedContent object"""
    return CollectedContent(
        id=sample_content_data['id'],
        platform=sample_content_data['platform'],
        content_type=sample_content_data['content_type'],
        text=sample_content_data['text'],
        author_id=sample_content_data['author_id'],
        author_username=sample_content_data['author_username'],
        created_at=sample_content_data['created_at'],
        engagement_metrics=sample_content_data['engagement_metrics'],
        hashtags=sample_content_data['hashtags'],
        mentions=sample_content_data['mentions'],
        urls=sample_content_data['urls'],
        location=sample_content_data['location'],
        language=sample_content_data['language'],
        media_urls=[],
        parent_id=None,
        raw_data=sample_content_data['raw_data']
    )


@pytest.fixture
def sample_trend_data():
    """Sample trend data for testing"""
    return {
        'keyword': 'artificial_intelligence',
        'platform': 'twitter',
        'platforms': ['twitter', 'reddit'],
        'mentions': 150,
        'velocity': 25.5,
        'acceleration': 2.1,
        'engagement_rate': 4.2,
        'geographic_spread': 5,
        'sentiment_score': 0.3,
        'trend_score': 0.75,
        'viral_probability': 0.68,
        'lifecycle_stage': 'growing',
        'confidence': 0.82,
        'first_seen': datetime.now(timezone.utc),
        'last_seen': datetime.now(timezone.utc),
        'extracted_at': datetime.now(timezone.utc)
    }


@pytest.fixture
def sample_alert_data():
    """Sample alert data for testing"""
    return {
        'id': 'alert_test_123',
        'type': 'trend_emergence',
        'severity': 'high',
        'keyword': 'breaking_news',
        'message': 'New trend detected with high viral potential',
        'platforms': ['twitter', 'reddit'],
        'metrics': {
            'trend_score': 0.85,
            'viral_probability': 0.78,
            'velocity': 45.2
        },
        'threshold_values': {
            'trend_score': 0.7,
            'viral_probability': 0.7
        },
        'created_at': datetime.now(timezone.utc)
    }


@pytest.fixture
def mock_twitter_api():
    """Mock Twitter API responses"""
    mock = AsyncMock()
    
    # Mock successful response
    mock.get.return_value.status = 200
    mock.get.return_value.json.return_value = {
        'data': [
            {
                'id': '1234567890',
                'text': 'Test tweet about #AI trends',
                'author_id': 'user123',
                'created_at': '2023-01-01T12:00:00.000Z',
                'public_metrics': {
                    'like_count': 10,
                    'retweet_count': 5,
                    'reply_count': 2
                }
            }
        ],
        'includes': {
            'users': [
                {
                    'id': 'user123',
                    'username': 'testuser',
                    'name': 'Test User'
                }
            ]
        }
    }
    
    return mock


@pytest.fixture
def mock_reddit_api():
    """Mock Reddit API responses"""
    mock = AsyncMock()
    
    mock.get.return_value.status = 200
    mock.get.return_value.json.return_value = {
        'data': {
            'children': [
                {
                    'data': {
                        'id': 'reddit123',
                        'title': 'Test Reddit post about AI',
                        'selftext': 'This is a test post',
                        'author': 'reddituser',
                        'created_utc': 1672574400,
                        'ups': 100,
                        'downs': 5,
                        'num_comments': 25,
                        'subreddit': 'technology'
                    }
                }
            ]
        }
    }
    
    return mock


@pytest.fixture
def mock_cache_manager():
    """Mock cache manager"""
    mock = AsyncMock()
    mock.get.return_value = None
    mock.set.return_value = True
    mock.exists.return_value = False
    mock.delete.return_value = True
    return mock


@pytest.fixture
def mock_rate_limiter():
    """Mock rate limiter"""
    mock = Mock()
    mock.is_allowed.return_value = True
    mock.time_until_reset.return_value = 0
    return mock


@pytest.fixture
def sample_batch_content():
    """Sample batch of content for testing"""
    content_list = []
    
    for i in range(10):
        content = CollectedContent(
            id=f'content_{i}',
            platform=Platform.TWITTER,
            content_type=ContentType.TEXT.value,
            text=f'Test content {i} with #hashtag{i}',
            author_id=f'user_{i}',
            author_username=f'testuser{i}',
            created_at=datetime.now(timezone.utc),
            engagement_metrics={'likes': i * 10, 'retweets': i * 2},
            hashtags=[f'hashtag{i}', 'test'],
            mentions=[],
            urls=[],
            location='Test City',
            language='en',
            media_urls=[],
            parent_id=None,
            raw_data={}
        )
        content_list.append(content)
    
    return content_list


@pytest.fixture
def temp_file():
    """Create a temporary file for testing"""
    with tempfile.NamedTemporaryFile(mode='w+', delete=False) as f:
        yield f.name
    
    # Clean up
    try:
        os.unlink(f.name)
    except OSError:
        pass


@pytest.fixture
def mock_settings():
    """Mock application settings"""
    return {
        'debug': True,
        'log_level': 'DEBUG',
        'rate_limit_requests': 1000,
        'rate_limit_window': 3600,
        'cache_ttl': 300,
        'data_retention_days': 30
    }


@pytest.fixture
async def mock_database():
    """Mock database for testing"""
    # Use in-memory SQLite for tests
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    
    engine = create_engine('sqlite:///:memory:', echo=False)
    Base.metadata.create_all(engine)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def api_client():
    """Test API client"""
    from fastapi.testclient import TestClient
    from src.main import app
    
    return TestClient(app)


# Test utilities

def create_test_content(platform: Platform = Platform.TWITTER, 
                       hashtags: List[str] = None,
                       sentiment: float = 0.0) -> CollectedContent:
    """Create test content with specified parameters"""
    if hashtags is None:
        hashtags = ['test']
    
    return CollectedContent(
        id=f'test_{platform.value}_{datetime.now().timestamp()}',
        platform=platform,
        content_type=ContentType.TEXT.value,
        text=f'Test content for {platform.value} with hashtags: {" ".join(f"#{tag}" for tag in hashtags)}',
        author_id='test_user',
        author_username='testuser',
        created_at=datetime.now(timezone.utc),
        engagement_metrics={'likes': 10, 'shares': 2},
        hashtags=hashtags,
        mentions=[],
        urls=[],
        location='Test Location',
        language='en',
        media_urls=[],
        parent_id=None,
        raw_data={'sentiment_score': sentiment}
    )


def assert_valid_trend_data(trend_data: Dict[str, Any]):
    """Assert that trend data has valid structure"""
    required_fields = ['keyword', 'platform', 'trend_score', 'viral_probability']
    
    for field in required_fields:
        assert field in trend_data, f"Missing required field: {field}"
    
    # Check data types
    assert isinstance(trend_data['keyword'], str)
    assert isinstance(trend_data['platform'], str)
    assert isinstance(trend_data['trend_score'], (int, float))
    assert isinstance(trend_data['viral_probability'], (int, float))
    
    # Check ranges
    assert 0 <= trend_data['trend_score'] <= 1
    assert 0 <= trend_data['viral_probability'] <= 1


def assert_valid_content_data(content_data: Dict[str, Any]):
    """Assert that content data has valid structure"""
    required_fields = ['id', 'platform', 'content_type']
    
    for field in required_fields:
        assert field in content_data, f"Missing required field: {field}"
    
    assert isinstance(content_data['id'], str)
    assert isinstance(content_data['platform'], str)
    assert isinstance(content_data['content_type'], str)


# Async test helpers

async def async_test_helper(coro):
    """Helper for running async tests"""
    return await coro


# Mock data generators

def generate_mock_trends(count: int = 5) -> List[Dict[str, Any]]:
    """Generate mock trend data"""
    trends = []
    
    for i in range(count):
        trend = {
            'keyword': f'trend_{i}',
            'platform': 'twitter',
            'mentions': 100 + i * 50,
            'velocity': 10.0 + i * 5,
            'trend_score': 0.5 + (i * 0.1),
            'viral_probability': 0.3 + (i * 0.1),
            'lifecycle_stage': 'growing',
            'created_at': datetime.now(timezone.utc)
        }
        trends.append(trend)
    
    return trends


def generate_mock_content(count: int = 10) -> List[Dict[str, Any]]:
    """Generate mock content data"""
    content_list = []
    
    for i in range(count):
        content = {
            'id': f'content_{i}',
            'platform': 'twitter',
            'content_type': 'text',
            'text': f'Mock content {i} with #hashtag{i}',
            'author_id': f'user_{i}',
            'hashtags': [f'hashtag{i}'],
            'engagement_metrics': {'likes': i * 10},
            'created_at': datetime.now(timezone.utc)
        }
        content_list.append(content)
    
    return content_list
