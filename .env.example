# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/trendradar
REDIS_URL=redis://localhost:6379/0

# Application Settings
SECRET_KEY=your-super-secret-key-change-this-in-production
DEBUG=True
LOG_LEVEL=INFO
ENVIRONMENT=development

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Social Media API Keys
# Twitter API v2 (Free tier available at developer.twitter.com)
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret

# Reddit API (Free at reddit.com/prefs/apps)
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=TrendRadar/1.0

# YouTube Data API v3 (Free tier at console.cloud.google.com)
YOUTUBE_API_KEY=your_youtube_api_key

# Instagram Basic Display API (Free at developers.facebook.com)
INSTAGRAM_ACCESS_TOKEN=your_instagram_access_token
INSTAGRAM_CLIENT_ID=your_instagram_client_id
INSTAGRAM_CLIENT_SECRET=your_instagram_client_secret

# Optional: Additional APIs
TIKTOK_ACCESS_TOKEN=your_tiktok_access_token
LINKEDIN_ACCESS_TOKEN=your_linkedin_access_token

# Caching Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=10000

# Rate Limiting
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=3600

# Monitoring & Analytics
ENABLE_METRICS=True
METRICS_PORT=9090

# Email Notifications (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret
WEBHOOK_URL=https://your-domain.com/webhooks

# Machine Learning Configuration
ML_MODEL_PATH=models/
ENABLE_GPU=False
BATCH_SIZE=32
MAX_SEQUENCE_LENGTH=512

# Geographic Configuration
DEFAULT_TIMEZONE=UTC
ENABLE_GEOLOCATION=True

# Alert Configuration
MAX_ALERTS_PER_USER=100
ALERT_COOLDOWN_MINUTES=5

# Data Retention
DATA_RETENTION_DAYS=90
CLEANUP_INTERVAL_HOURS=24

# Security
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24
PASSWORD_MIN_LENGTH=8

# Frontend Configuration (if applicable)
FRONTEND_URL=http://localhost:3000
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Development Tools
ENABLE_SWAGGER=True
ENABLE_REDOC=True
ENABLE_DEBUG_TOOLBAR=False
