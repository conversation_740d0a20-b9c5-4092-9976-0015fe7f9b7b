"""
Validation Utilities for TrendRadar
Input validation, data sanitization, and security checks
"""

import re
import logging
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timezone
from urllib.parse import urlparse
import ipaddress
import json

from src.utils.constants import Platform, ContentType, AlertType, AlertSeverity

logger = logging.getLogger(__name__)


class ValidationError(Exception):
    """Custom validation error"""
    def __init__(self, message: str, field: str = None, code: str = None):
        self.message = message
        self.field = field
        self.code = code
        super().__init__(message)


class DataValidator:
    """Main data validation class"""
    
    def __init__(self):
        # Regex patterns for validation
        self.patterns = {
            'email': re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
            'username': re.compile(r'^[a-zA-Z0-9_-]{3,30}$'),
            'hashtag': re.compile(r'^#?[a-zA-Z0-9_]{1,50}$'),
            'url': re.compile(r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$'),
            'api_key': re.compile(r'^[a-zA-Z0-9_-]{20,100}$'),
            'uuid': re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'),
            'safe_string': re.compile(r'^[a-zA-Z0-9\s\-_.,!?()]+$'),
            'keyword': re.compile(r'^[a-zA-Z0-9\s_-]{1,100}$')
        }
        
        # Dangerous patterns to reject
        self.dangerous_patterns = [
            re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL),
            re.compile(r'javascript:', re.IGNORECASE),
            re.compile(r'on\w+\s*=', re.IGNORECASE),
            re.compile(r'<iframe[^>]*>.*?</iframe>', re.IGNORECASE | re.DOTALL),
            re.compile(r'<object[^>]*>.*?</object>', re.IGNORECASE | re.DOTALL),
            re.compile(r'<embed[^>]*>', re.IGNORECASE),
            re.compile(r'vbscript:', re.IGNORECASE),
            re.compile(r'data:text/html', re.IGNORECASE)
        ]
    
    def validate_email(self, email: str) -> bool:
        """Validate email address"""
        if not email or not isinstance(email, str):
            return False
        
        if len(email) > 254:  # RFC 5321 limit
            return False
        
        return bool(self.patterns['email'].match(email.strip().lower()))
    
    def validate_username(self, username: str) -> bool:
        """Validate username"""
        if not username or not isinstance(username, str):
            return False
        
        return bool(self.patterns['username'].match(username.strip()))
    
    def validate_password(self, password: str) -> Tuple[bool, List[str]]:
        """Validate password strength"""
        if not password or not isinstance(password, str):
            return False, ["Password is required"]
        
        errors = []
        
        if len(password) < 8:
            errors.append("Password must be at least 8 characters long")
        
        if len(password) > 128:
            errors.append("Password must be less than 128 characters")
        
        if not re.search(r'[a-z]', password):
            errors.append("Password must contain at least one lowercase letter")
        
        if not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter")
        
        if not re.search(r'\d', password):
            errors.append("Password must contain at least one digit")
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("Password must contain at least one special character")
        
        return len(errors) == 0, errors
    
    def validate_url(self, url: str) -> bool:
        """Validate URL format and safety"""
        if not url or not isinstance(url, str):
            return False
        
        try:
            # Basic format check
            if not self.patterns['url'].match(url):
                return False
            
            # Parse URL
            parsed = urlparse(url)
            
            # Check scheme
            if parsed.scheme not in ['http', 'https']:
                return False
            
            # Check for dangerous patterns
            if self._contains_dangerous_content(url):
                return False
            
            # Check domain
            if not parsed.netloc:
                return False
            
            # Reject localhost and private IPs in production
            if parsed.hostname:
                try:
                    ip = ipaddress.ip_address(parsed.hostname)
                    if ip.is_private or ip.is_loopback:
                        return False
                except ValueError:
                    # Not an IP address, check domain
                    if parsed.hostname.lower() in ['localhost', '127.0.0.1', '0.0.0.0']:
                        return False
            
            return True
            
        except Exception as e:
            logger.warning(f"URL validation error: {e}")
            return False
    
    def validate_hashtag(self, hashtag: str) -> bool:
        """Validate hashtag format"""
        if not hashtag or not isinstance(hashtag, str):
            return False
        
        # Remove # if present
        clean_hashtag = hashtag.lstrip('#')
        
        return bool(self.patterns['hashtag'].match(f"#{clean_hashtag}"))
    
    def validate_api_key(self, api_key: str) -> bool:
        """Validate API key format"""
        if not api_key or not isinstance(api_key, str):
            return False
        
        return bool(self.patterns['api_key'].match(api_key))
    
    def validate_platform(self, platform: str) -> bool:
        """Validate platform name"""
        if not platform or not isinstance(platform, str):
            return False
        
        try:
            Platform(platform.lower())
            return True
        except ValueError:
            return False
    
    def validate_content_type(self, content_type: str) -> bool:
        """Validate content type"""
        if not content_type or not isinstance(content_type, str):
            return False
        
        try:
            ContentType(content_type.lower())
            return True
        except ValueError:
            return False
    
    def validate_alert_type(self, alert_type: str) -> bool:
        """Validate alert type"""
        if not alert_type or not isinstance(alert_type, str):
            return False
        
        try:
            AlertType(alert_type.lower())
            return True
        except ValueError:
            return False
    
    def validate_alert_severity(self, severity: str) -> bool:
        """Validate alert severity"""
        if not severity or not isinstance(severity, str):
            return False
        
        try:
            AlertSeverity(severity.lower())
            return True
        except ValueError:
            return False
    
    def validate_datetime(self, dt_string: str) -> bool:
        """Validate datetime string"""
        if not dt_string or not isinstance(dt_string, str):
            return False
        
        try:
            # Try ISO format
            datetime.fromisoformat(dt_string.replace('Z', '+00:00'))
            return True
        except ValueError:
            try:
                # Try common formats
                datetime.strptime(dt_string, '%Y-%m-%d %H:%M:%S')
                return True
            except ValueError:
                try:
                    datetime.strptime(dt_string, '%Y-%m-%d')
                    return True
                except ValueError:
                    return False
    
    def validate_ip_address(self, ip: str) -> bool:
        """Validate IP address"""
        if not ip or not isinstance(ip, str):
            return False
        
        try:
            ipaddress.ip_address(ip)
            return True
        except ValueError:
            return False
    
    def validate_json(self, json_string: str) -> bool:
        """Validate JSON string"""
        if not json_string or not isinstance(json_string, str):
            return False
        
        try:
            json.loads(json_string)
            return True
        except (json.JSONDecodeError, ValueError):
            return False
    
    def sanitize_string(self, text: str, max_length: int = 1000) -> str:
        """Sanitize string input"""
        if not text or not isinstance(text, str):
            return ""
        
        # Truncate if too long
        text = text[:max_length]
        
        # Remove dangerous patterns
        for pattern in self.dangerous_patterns:
            text = pattern.sub('', text)
        
        # Remove control characters except newlines and tabs
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def sanitize_hashtag(self, hashtag: str) -> str:
        """Sanitize hashtag"""
        if not hashtag or not isinstance(hashtag, str):
            return ""
        
        # Remove # and clean
        clean = hashtag.lstrip('#').strip()
        
        # Keep only alphanumeric and underscores
        clean = re.sub(r'[^a-zA-Z0-9_]', '', clean)
        
        # Limit length
        clean = clean[:50]
        
        return clean.lower() if clean else ""
    
    def sanitize_url(self, url: str) -> str:
        """Sanitize URL"""
        if not url or not isinstance(url, str):
            return ""
        
        url = url.strip()
        
        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        # Validate and return
        if self.validate_url(url):
            return url
        
        return ""
    
    def _contains_dangerous_content(self, text: str) -> bool:
        """Check if text contains dangerous patterns"""
        for pattern in self.dangerous_patterns:
            if pattern.search(text):
                return True
        return False
    
    def validate_trend_data(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate trend data structure"""
        errors = []
        
        # Required fields
        required_fields = ['keyword', 'platform']
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
            elif not data[field]:
                errors.append(f"Field {field} cannot be empty")
        
        # Validate specific fields
        if 'keyword' in data:
            keyword = data['keyword']
            if not isinstance(keyword, str) or len(keyword) > 255:
                errors.append("Keyword must be a string with max 255 characters")
        
        if 'platform' in data and not self.validate_platform(data['platform']):
            errors.append("Invalid platform")
        
        # Validate numeric fields
        numeric_fields = ['mentions', 'velocity', 'acceleration', 'engagement_rate', 
                         'geographic_spread', 'sentiment_score', 'trend_score', 'viral_probability']
        
        for field in numeric_fields:
            if field in data:
                value = data[field]
                if not isinstance(value, (int, float)):
                    errors.append(f"Field {field} must be numeric")
                elif field in ['trend_score', 'viral_probability'] and not (0 <= value <= 1):
                    errors.append(f"Field {field} must be between 0 and 1")
                elif field == 'sentiment_score' and not (-1 <= value <= 1):
                    errors.append(f"Field {field} must be between -1 and 1")
        
        # Validate arrays
        array_fields = ['platforms', 'hashtags']
        for field in array_fields:
            if field in data:
                value = data[field]
                if not isinstance(value, list):
                    errors.append(f"Field {field} must be an array")
                elif len(value) > 100:
                    errors.append(f"Field {field} cannot have more than 100 items")
        
        return len(errors) == 0, errors
    
    def validate_content_data(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate content data structure"""
        errors = []
        
        # Required fields
        required_fields = ['id', 'platform', 'content_type']
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
        
        # Validate ID
        if 'id' in data:
            content_id = data['id']
            if not isinstance(content_id, str) or len(content_id) > 255:
                errors.append("Content ID must be a string with max 255 characters")
        
        # Validate platform and content type
        if 'platform' in data and not self.validate_platform(data['platform']):
            errors.append("Invalid platform")
        
        if 'content_type' in data and not self.validate_content_type(data['content_type']):
            errors.append("Invalid content type")
        
        # Validate text length
        if 'text' in data:
            text = data['text']
            if text and len(text) > 10000:  # 10k character limit
                errors.append("Text content too long (max 10,000 characters)")
        
        # Validate URLs in content
        if 'urls' in data:
            urls = data['urls']
            if isinstance(urls, list):
                for url in urls:
                    if not self.validate_url(url):
                        errors.append(f"Invalid URL: {url}")
        
        # Validate hashtags
        if 'hashtags' in data:
            hashtags = data['hashtags']
            if isinstance(hashtags, list):
                for hashtag in hashtags:
                    if not self.validate_hashtag(hashtag):
                        errors.append(f"Invalid hashtag: {hashtag}")
        
        return len(errors) == 0, errors
    
    def validate_alert_data(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate alert data structure"""
        errors = []
        
        # Required fields
        required_fields = ['type', 'severity', 'keyword', 'message']
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
        
        # Validate alert type and severity
        if 'type' in data and not self.validate_alert_type(data['type']):
            errors.append("Invalid alert type")
        
        if 'severity' in data and not self.validate_alert_severity(data['severity']):
            errors.append("Invalid alert severity")
        
        # Validate message length
        if 'message' in data:
            message = data['message']
            if not isinstance(message, str) or len(message) > 1000:
                errors.append("Message must be a string with max 1000 characters")
        
        return len(errors) == 0, errors
    
    def validate_pagination_params(self, params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate pagination parameters"""
        errors = []
        
        # Validate limit
        if 'limit' in params:
            limit = params['limit']
            try:
                limit = int(limit)
                if limit < 1 or limit > 1000:
                    errors.append("Limit must be between 1 and 1000")
            except (ValueError, TypeError):
                errors.append("Limit must be a valid integer")
        
        # Validate offset
        if 'offset' in params:
            offset = params['offset']
            try:
                offset = int(offset)
                if offset < 0:
                    errors.append("Offset must be non-negative")
            except (ValueError, TypeError):
                errors.append("Offset must be a valid integer")
        
        # Validate sort parameters
        if 'sort_by' in params:
            sort_by = params['sort_by']
            allowed_sort_fields = [
                'created_at', 'updated_at', 'trend_score', 'viral_probability',
                'mentions', 'velocity', 'engagement_rate'
            ]
            if sort_by not in allowed_sort_fields:
                errors.append(f"Invalid sort field. Allowed: {', '.join(allowed_sort_fields)}")
        
        if 'sort_order' in params:
            sort_order = params['sort_order']
            if sort_order not in ['asc', 'desc']:
                errors.append("Sort order must be 'asc' or 'desc'")
        
        return len(errors) == 0, errors


# Global validator instance
validator = DataValidator()


def validate_request_data(data: Dict[str, Any], data_type: str) -> Dict[str, Any]:
    """Validate request data and return sanitized version"""
    if data_type == 'trend':
        valid, errors = validator.validate_trend_data(data)
    elif data_type == 'content':
        valid, errors = validator.validate_content_data(data)
    elif data_type == 'alert':
        valid, errors = validator.validate_alert_data(data)
    else:
        raise ValidationError(f"Unknown data type: {data_type}")
    
    if not valid:
        raise ValidationError(f"Validation failed: {'; '.join(errors)}")
    
    # Sanitize string fields
    sanitized_data = {}
    for key, value in data.items():
        if isinstance(value, str):
            if key in ['text', 'message', 'description']:
                sanitized_data[key] = validator.sanitize_string(value, max_length=10000)
            elif key in ['keyword', 'title']:
                sanitized_data[key] = validator.sanitize_string(value, max_length=255)
            elif key.endswith('_url') or key == 'url':
                sanitized_data[key] = validator.sanitize_url(value)
            else:
                sanitized_data[key] = validator.sanitize_string(value)
        elif isinstance(value, list) and key == 'hashtags':
            sanitized_data[key] = [validator.sanitize_hashtag(tag) for tag in value if tag]
        else:
            sanitized_data[key] = value
    
    return sanitized_data
