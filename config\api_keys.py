"""
API Keys and Authentication Management for TrendRadar
Secure handling of social media API credentials
"""

import os
from typing import Dict, Optional, Any
from dataclasses import dataclass
from config.settings import settings
import logging

logger = logging.getLogger(__name__)


@dataclass
class APICredentials:
    """Base class for API credentials"""
    platform: str
    is_configured: bool = False
    rate_limit: Optional[Dict[str, Any]] = None


@dataclass
class TwitterCredentials(APICredentials):
    """Twitter API credentials"""
    bearer_token: Optional[str] = None
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    access_token: Optional[str] = None
    access_token_secret: Optional[str] = None
    
    def __post_init__(self):
        self.platform = "twitter"
        self.bearer_token = settings.twitter_bearer_token
        self.api_key = settings.twitter_api_key
        self.api_secret = settings.twitter_api_secret
        self.access_token = settings.twitter_access_token
        self.access_token_secret = settings.twitter_access_token_secret
        
        # Check if credentials are configured
        self.is_configured = bool(
            self.bearer_token or 
            (self.api_key and self.api_secret and 
             self.access_token and self.access_token_secret)
        )
        
        self.rate_limit = {
            "requests_per_window": 300,
            "window_minutes": 15,
            "burst_limit": 50
        }


@dataclass
class RedditCredentials(APICredentials):
    """Reddit API credentials"""
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    user_agent: str = "TrendRadar/1.0"
    
    def __post_init__(self):
        self.platform = "reddit"
        self.client_id = settings.reddit_client_id
        self.client_secret = settings.reddit_client_secret
        self.user_agent = settings.reddit_user_agent
        
        self.is_configured = bool(self.client_id and self.client_secret)
        
        self.rate_limit = {
            "requests_per_window": 60,
            "window_minutes": 1,
            "burst_limit": 10
        }


@dataclass
class YouTubeCredentials(APICredentials):
    """YouTube Data API credentials"""
    api_key: Optional[str] = None
    
    def __post_init__(self):
        self.platform = "youtube"
        self.api_key = settings.youtube_api_key
        
        self.is_configured = bool(self.api_key)
        
        self.rate_limit = {
            "requests_per_window": 10000,
            "window_minutes": 1440,  # Daily limit
            "burst_limit": 100
        }


@dataclass
class InstagramCredentials(APICredentials):
    """Instagram Basic Display API credentials"""
    access_token: Optional[str] = None
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    
    def __post_init__(self):
        self.platform = "instagram"
        self.access_token = settings.instagram_access_token
        self.client_id = settings.instagram_client_id
        self.client_secret = settings.instagram_client_secret
        
        self.is_configured = bool(self.access_token)
        
        self.rate_limit = {
            "requests_per_window": 200,
            "window_minutes": 60,
            "burst_limit": 20
        }


@dataclass
class TikTokCredentials(APICredentials):
    """TikTok API credentials (optional)"""
    access_token: Optional[str] = None
    
    def __post_init__(self):
        self.platform = "tiktok"
        self.access_token = settings.tiktok_access_token
        
        self.is_configured = bool(self.access_token)
        
        self.rate_limit = {
            "requests_per_window": 1000,
            "window_minutes": 60,
            "burst_limit": 50
        }


@dataclass
class LinkedInCredentials(APICredentials):
    """LinkedIn API credentials (optional)"""
    access_token: Optional[str] = None
    
    def __post_init__(self):
        self.platform = "linkedin"
        self.access_token = settings.linkedin_access_token
        
        self.is_configured = bool(self.access_token)
        
        self.rate_limit = {
            "requests_per_window": 500,
            "window_minutes": 60,
            "burst_limit": 25
        }


class APIKeyManager:
    """Centralized API key management"""
    
    def __init__(self):
        self.credentials = {
            "twitter": TwitterCredentials(),
            "reddit": RedditCredentials(),
            "youtube": YouTubeCredentials(),
            "instagram": InstagramCredentials(),
            "tiktok": TikTokCredentials(),
            "linkedin": LinkedInCredentials()
        }
        
        self._validate_credentials()
    
    def _validate_credentials(self):
        """Validate and log credential status"""
        configured_platforms = []
        missing_platforms = []
        
        for platform, creds in self.credentials.items():
            if creds.is_configured:
                configured_platforms.append(platform)
                logger.info(f"{platform.title()} API credentials configured")
            else:
                missing_platforms.append(platform)
                logger.warning(f"{platform.title()} API credentials not configured")
        
        if not configured_platforms:
            logger.error("No API credentials configured! TrendRadar requires at least one platform.")
        else:
            logger.info(f"Configured platforms: {', '.join(configured_platforms)}")
        
        if missing_platforms:
            logger.info(f"Missing platforms: {', '.join(missing_platforms)}")
    
    def get_credentials(self, platform: str) -> Optional[APICredentials]:
        """Get credentials for a specific platform"""
        return self.credentials.get(platform.lower())
    
    def is_platform_configured(self, platform: str) -> bool:
        """Check if a platform is configured"""
        creds = self.get_credentials(platform)
        return creds.is_configured if creds else False
    
    def get_configured_platforms(self) -> list:
        """Get list of configured platforms"""
        return [
            platform for platform, creds in self.credentials.items()
            if creds.is_configured
        ]
    
    def get_rate_limit(self, platform: str) -> Optional[Dict[str, Any]]:
        """Get rate limit configuration for a platform"""
        creds = self.get_credentials(platform)
        return creds.rate_limit if creds else None
    
    def update_credentials(self, platform: str, **kwargs):
        """Update credentials for a platform"""
        if platform in self.credentials:
            creds = self.credentials[platform]
            for key, value in kwargs.items():
                if hasattr(creds, key):
                    setattr(creds, key, value)
            
            # Re-validate after update
            creds.__post_init__()
            logger.info(f"Updated credentials for {platform}")
        else:
            logger.error(f"Unknown platform: {platform}")


# Global API key manager instance
api_manager = APIKeyManager()


def get_api_manager() -> APIKeyManager:
    """Get the global API manager instance"""
    return api_manager


# Helper functions for common operations
def get_twitter_auth():
    """Get Twitter authentication configuration"""
    creds = api_manager.get_credentials("twitter")
    if not creds or not creds.is_configured:
        return None
    
    if creds.bearer_token:
        return {"bearer_token": creds.bearer_token}
    else:
        return {
            "consumer_key": creds.api_key,
            "consumer_secret": creds.api_secret,
            "access_token": creds.access_token,
            "access_token_secret": creds.access_token_secret
        }


def get_reddit_auth():
    """Get Reddit authentication configuration"""
    creds = api_manager.get_credentials("reddit")
    if not creds or not creds.is_configured:
        return None
    
    return {
        "client_id": creds.client_id,
        "client_secret": creds.client_secret,
        "user_agent": creds.user_agent
    }


def get_youtube_auth():
    """Get YouTube authentication configuration"""
    creds = api_manager.get_credentials("youtube")
    if not creds or not creds.is_configured:
        return None
    
    return {"api_key": creds.api_key}


def get_instagram_auth():
    """Get Instagram authentication configuration"""
    creds = api_manager.get_credentials("instagram")
    if not creds or not creds.is_configured:
        return None
    
    return {
        "access_token": creds.access_token,
        "client_id": creds.client_id,
        "client_secret": creds.client_secret
    }


# API endpoint configurations
API_ENDPOINTS = {
    "twitter": {
        "base_url": "https://api.twitter.com/2",
        "search": "/tweets/search/recent",
        "user_tweets": "/users/{user_id}/tweets",
        "trends": "/trends/by/woeid/{woeid}"
    },
    "reddit": {
        "base_url": "https://www.reddit.com",
        "subreddit": "/r/{subreddit}",
        "search": "/search",
        "hot": "/hot",
        "new": "/new"
    },
    "youtube": {
        "base_url": "https://www.googleapis.com/youtube/v3",
        "search": "/search",
        "videos": "/videos",
        "channels": "/channels",
        "comments": "/commentThreads"
    },
    "instagram": {
        "base_url": "https://graph.instagram.com",
        "media": "/me/media",
        "user": "/me"
    }
}


def get_api_endpoint(platform: str, endpoint_type: str) -> Optional[str]:
    """Get API endpoint URL for a platform and endpoint type"""
    platform_config = API_ENDPOINTS.get(platform.lower())
    if not platform_config:
        return None
    
    base_url = platform_config.get("base_url", "")
    endpoint = platform_config.get(endpoint_type, "")
    
    return f"{base_url}{endpoint}" if endpoint else None


# Security utilities
def mask_api_key(api_key: str, visible_chars: int = 4) -> str:
    """Mask API key for logging purposes"""
    if not api_key or len(api_key) <= visible_chars:
        return "***"
    
    return f"{api_key[:visible_chars]}{'*' * (len(api_key) - visible_chars)}"


def validate_api_key_format(platform: str, api_key: str) -> bool:
    """Validate API key format for different platforms"""
    if not api_key:
        return False
    
    # Basic validation rules (can be expanded)
    validation_rules = {
        "twitter": lambda key: len(key) >= 25,
        "reddit": lambda key: len(key) >= 14,
        "youtube": lambda key: len(key) >= 39,
        "instagram": lambda key: len(key) >= 20
    }
    
    validator = validation_rules.get(platform.lower())
    return validator(api_key) if validator else True
