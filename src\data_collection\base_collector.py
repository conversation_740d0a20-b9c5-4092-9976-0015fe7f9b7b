"""
Base Data Collector for TrendRadar
Abstract base class and manager for social media data collection
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass

from config.settings import settings
from config.api_keys import get_api_manager
from src.utils.helpers import RateLimiter, retry_async, get_current_timestamp
from src.utils.constants import Platform, DataCollectionStatus, DEFAULT_CONFIG

logger = logging.getLogger(__name__)


@dataclass
class CollectedContent:
    """Standardized structure for collected social media content"""
    id: str
    platform: Platform
    content_type: str
    text: str
    author_id: str
    author_username: str
    created_at: datetime
    engagement_metrics: Dict[str, int]
    hashtags: List[str]
    mentions: List[str]
    urls: List[str]
    location: Optional[str] = None
    language: Optional[str] = None
    media_urls: List[str] = None
    parent_id: Optional[str] = None  # For replies/comments
    raw_data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.media_urls is None:
            self.media_urls = []
        if self.raw_data is None:
            self.raw_data = {}


@dataclass
class CollectionMetrics:
    """Metrics for data collection performance"""
    platform: Platform
    total_collected: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    rate_limited_requests: int = 0
    last_collection_time: Optional[datetime] = None
    average_response_time: float = 0.0
    status: DataCollectionStatus = DataCollectionStatus.ACTIVE


class BaseCollector(ABC):
    """Abstract base class for social media data collectors"""
    
    def __init__(self, platform: Platform):
        self.platform = platform
        self.api_manager = get_api_manager()
        self.credentials = self.api_manager.get_credentials(platform.value)
        self.rate_limiter = None
        self.metrics = CollectionMetrics(platform)
        self.is_running = False
        
        if self.credentials and self.credentials.rate_limit:
            self.rate_limiter = RateLimiter(
                max_requests=self.credentials.rate_limit["requests_per_window"],
                window_seconds=self.credentials.rate_limit["window_minutes"] * 60
            )
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the collector and test API connection"""
        pass
    
    @abstractmethod
    async def collect_trending_topics(self, limit: int = 50) -> List[str]:
        """Collect trending topics/hashtags"""
        pass
    
    @abstractmethod
    async def search_content(self, query: str, limit: int = 100) -> List[CollectedContent]:
        """Search for content based on query"""
        pass
    
    @abstractmethod
    async def get_user_content(self, user_id: str, limit: int = 50) -> List[CollectedContent]:
        """Get recent content from a specific user"""
        pass
    
    @abstractmethod
    async def get_content_details(self, content_id: str) -> Optional[CollectedContent]:
        """Get detailed information about specific content"""
        pass
    
    def is_configured(self) -> bool:
        """Check if collector is properly configured"""
        return self.credentials and self.credentials.is_configured
    
    def is_healthy(self) -> bool:
        """Check if collector is healthy and operational"""
        return (
            self.is_configured() and
            self.metrics.status == DataCollectionStatus.ACTIVE and
            self.metrics.failed_requests < self.metrics.successful_requests * 0.5
        )
    
    async def check_rate_limit(self) -> bool:
        """Check if request is within rate limits"""
        if not self.rate_limiter:
            return True
        
        if not self.rate_limiter.is_allowed():
            self.metrics.status = DataCollectionStatus.RATE_LIMITED
            wait_time = self.rate_limiter.time_until_reset()
            logger.warning(f"{self.platform.value} rate limited, waiting {wait_time:.1f}s")
            await asyncio.sleep(wait_time)
            return False
        
        return True
    
    async def make_request(self, request_func, *args, **kwargs) -> Any:
        """Make API request with error handling and rate limiting"""
        if not await self.check_rate_limit():
            return None
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            result = await retry_async(
                lambda: request_func(*args, **kwargs),
                max_retries=DEFAULT_CONFIG["retry_attempts"],
                delay=DEFAULT_CONFIG["retry_delay"]
            )
            
            # Update metrics
            self.metrics.successful_requests += 1
            response_time = asyncio.get_event_loop().time() - start_time
            self.metrics.average_response_time = (
                (self.metrics.average_response_time * (self.metrics.successful_requests - 1) + response_time) /
                self.metrics.successful_requests
            )
            
            if self.metrics.status == DataCollectionStatus.RATE_LIMITED:
                self.metrics.status = DataCollectionStatus.ACTIVE
            
            return result
            
        except Exception as e:
            self.metrics.failed_requests += 1
            self.metrics.status = DataCollectionStatus.ERROR
            logger.error(f"{self.platform.value} request failed: {e}")
            return None
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get collection metrics"""
        return {
            "platform": self.platform.value,
            "total_collected": self.metrics.total_collected,
            "successful_requests": self.metrics.successful_requests,
            "failed_requests": self.metrics.failed_requests,
            "rate_limited_requests": self.metrics.rate_limited_requests,
            "last_collection_time": self.metrics.last_collection_time.isoformat() if self.metrics.last_collection_time else None,
            "average_response_time": self.metrics.average_response_time,
            "status": self.metrics.status.value,
            "success_rate": (
                self.metrics.successful_requests / 
                (self.metrics.successful_requests + self.metrics.failed_requests)
                if (self.metrics.successful_requests + self.metrics.failed_requests) > 0 else 0
            )
        }


class DataCollectionManager:
    """Manager for coordinating multiple data collectors"""
    
    def __init__(self):
        self.collectors: Dict[Platform, BaseCollector] = {}
        self.collection_tasks: Dict[Platform, asyncio.Task] = {}
        self.is_running = False
        self.collection_interval = DEFAULT_CONFIG["data_collection_interval"]
    
    async def initialize(self):
        """Initialize all available collectors"""
        logger.info("Initializing data collection manager...")
        
        # Import collectors dynamically to avoid circular imports
        from src.data_collection.twitter_collector import TwitterCollector
        from src.data_collection.reddit_collector import RedditCollector
        from src.data_collection.youtube_collector import YouTubeCollector
        from src.data_collection.instagram_collector import InstagramCollector
        
        # Initialize collectors for configured platforms
        collector_classes = {
            Platform.TWITTER: TwitterCollector,
            Platform.REDDIT: RedditCollector,
            Platform.YOUTUBE: YouTubeCollector,
            Platform.INSTAGRAM: InstagramCollector
        }
        
        for platform, collector_class in collector_classes.items():
            try:
                collector = collector_class(platform)
                if collector.is_configured():
                    if await collector.initialize():
                        self.collectors[platform] = collector
                        logger.info(f"Initialized {platform.value} collector")
                    else:
                        logger.error(f"Failed to initialize {platform.value} collector")
                else:
                    logger.warning(f"{platform.value} collector not configured")
            except Exception as e:
                logger.error(f"Error initializing {platform.value} collector: {e}")
        
        if not self.collectors:
            logger.error("No collectors initialized! Check API configurations.")
        else:
            logger.info(f"Initialized {len(self.collectors)} collectors")
    
    async def start_collection(self):
        """Start data collection for all platforms"""
        if self.is_running:
            logger.warning("Data collection already running")
            return
        
        self.is_running = True
        logger.info("Starting data collection...")
        
        # Start collection tasks for each platform
        for platform, collector in self.collectors.items():
            task = asyncio.create_task(self._collection_loop(collector))
            self.collection_tasks[platform] = task
            logger.info(f"Started collection task for {platform.value}")
    
    async def stop_collection(self):
        """Stop data collection for all platforms"""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("Stopping data collection...")
        
        # Cancel all collection tasks
        for platform, task in self.collection_tasks.items():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            logger.info(f"Stopped collection task for {platform.value}")
        
        self.collection_tasks.clear()
    
    async def _collection_loop(self, collector: BaseCollector):
        """Main collection loop for a specific collector"""
        while self.is_running:
            try:
                if collector.is_healthy():
                    await self._collect_platform_data(collector)
                else:
                    logger.warning(f"{collector.platform.value} collector unhealthy, skipping collection")
                
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in {collector.platform.value} collection loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _collect_platform_data(self, collector: BaseCollector):
        """Collect data from a specific platform"""
        try:
            # Collect trending topics
            trending_topics = await collector.collect_trending_topics(limit=20)
            
            # Search for content based on trending topics
            all_content = []
            for topic in trending_topics[:5]:  # Limit to top 5 topics
                content = await collector.search_content(topic, limit=20)
                all_content.extend(content)
            
            # Update metrics
            collector.metrics.total_collected += len(all_content)
            collector.metrics.last_collection_time = get_current_timestamp()
            
            # Store collected content (will be implemented with database models)
            if all_content:
                await self._store_collected_content(all_content)
                logger.info(f"Collected {len(all_content)} items from {collector.platform.value}")
            
        except Exception as e:
            logger.error(f"Error collecting data from {collector.platform.value}: {e}")
    
    async def _store_collected_content(self, content_list: List[CollectedContent]):
        """Store collected content in database"""
        # This will be implemented when database models are ready
        # For now, just log the collection
        logger.debug(f"Storing {len(content_list)} content items")
    
    def get_collector(self, platform: Platform) -> Optional[BaseCollector]:
        """Get collector for specific platform"""
        return self.collectors.get(platform)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get metrics for all collectors"""
        metrics = {
            "total_collectors": len(self.collectors),
            "active_collectors": sum(1 for c in self.collectors.values() if c.is_healthy()),
            "is_running": self.is_running,
            "collection_interval": self.collection_interval,
            "platforms": {}
        }
        
        for platform, collector in self.collectors.items():
            metrics["platforms"][platform.value] = collector.get_metrics()
        
        return metrics
    
    def is_healthy(self) -> bool:
        """Check if data collection manager is healthy"""
        if not self.collectors:
            return False
        
        healthy_collectors = sum(1 for c in self.collectors.values() if c.is_healthy())
        return healthy_collectors > 0
    
    async def search_across_platforms(self, query: str, platforms: Optional[List[Platform]] = None) -> Dict[Platform, List[CollectedContent]]:
        """Search for content across multiple platforms"""
        if platforms is None:
            platforms = list(self.collectors.keys())
        
        results = {}
        
        for platform in platforms:
            collector = self.collectors.get(platform)
            if collector and collector.is_healthy():
                try:
                    content = await collector.search_content(query, limit=50)
                    results[platform] = content
                except Exception as e:
                    logger.error(f"Error searching {platform.value} for '{query}': {e}")
                    results[platform] = []
            else:
                results[platform] = []
        
        return results
    
    async def get_trending_topics_all_platforms(self) -> Dict[Platform, List[str]]:
        """Get trending topics from all platforms"""
        results = {}
        
        for platform, collector in self.collectors.items():
            if collector.is_healthy():
                try:
                    topics = await collector.collect_trending_topics(limit=20)
                    results[platform] = topics
                except Exception as e:
                    logger.error(f"Error getting trending topics from {platform.value}: {e}")
                    results[platform] = []
            else:
                results[platform] = []
        
        return results
