"""
Trend Prediction Engine for TrendRadar
Machine learning models for trend forecasting and viral prediction
"""

import logging
import asyncio
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
import statistics
import json

from src.utils.helpers import calculate_velocity, calculate_acceleration
from src.utils.constants import TrendStage, Platform, ML_MODEL_CONFIGS
from database.models.trend import Trend, TrendPrediction

logger = logging.getLogger(__name__)


@dataclass
class PredictionResult:
    """Result of a trend prediction"""
    keyword: str
    prediction_type: str
    predicted_value: float
    confidence: float
    horizon_hours: int
    model_name: str
    features_used: Dict[str, Any]
    prediction_time: datetime
    target_time: datetime
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class TrendPredictor:
    """Main trend prediction engine using multiple ML approaches"""
    
    def __init__(self):
        self.models = {}
        self.feature_extractors = {}
        self.prediction_cache = {}
        self.model_configs = ML_MODEL_CONFIGS
        
        # Initialize prediction models
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize prediction models"""
        try:
            # Simple statistical models (no external ML libraries required)
            self.models = {
                'linear_trend': LinearTrendModel(),
                'exponential_growth': ExponentialGrowthModel(),
                'viral_classifier': ViralClassifier(),
                'peak_predictor': PeakPredictor(),
                'lifecycle_predictor': LifecyclePredictor()
            }
            
            logger.info(f"Initialized {len(self.models)} prediction models")
            
        except Exception as e:
            logger.error(f"Error initializing models: {e}")
            self.models = {}
    
    async def predict_trend_future(self, trend_data: Dict[str, Any], 
                                 horizon_hours: int = 24) -> List[PredictionResult]:
        """Predict future trend metrics"""
        try:
            predictions = []
            
            # Extract features for prediction
            features = self._extract_features(trend_data)
            
            # Make predictions with different models
            for model_name, model in self.models.items():
                try:
                    if hasattr(model, 'predict_mentions'):
                        result = await self._predict_mentions(
                            model, trend_data, features, horizon_hours
                        )
                        if result:
                            predictions.append(result)
                    
                    if hasattr(model, 'predict_viral_probability'):
                        result = await self._predict_viral_probability(
                            model, trend_data, features, horizon_hours
                        )
                        if result:
                            predictions.append(result)
                    
                    if hasattr(model, 'predict_peak_time'):
                        result = await self._predict_peak_time(
                            model, trend_data, features, horizon_hours
                        )
                        if result:
                            predictions.append(result)
                            
                except Exception as e:
                    logger.error(f"Error with model {model_name}: {e}")
                    continue
            
            logger.info(f"Generated {len(predictions)} predictions for {trend_data.get('keyword', 'unknown')}")
            return predictions
            
        except Exception as e:
            logger.error(f"Error predicting trend future: {e}")
            return []
    
    async def _predict_mentions(self, model, trend_data: Dict[str, Any], 
                              features: Dict[str, Any], horizon_hours: int) -> Optional[PredictionResult]:
        """Predict future mention count"""
        try:
            predicted_mentions = model.predict_mentions(features, horizon_hours)
            confidence = model.get_confidence(features, 'mentions')
            
            return PredictionResult(
                keyword=trend_data.get('keyword', ''),
                prediction_type='mentions',
                predicted_value=predicted_mentions,
                confidence=confidence,
                horizon_hours=horizon_hours,
                model_name=model.__class__.__name__,
                features_used=features,
                prediction_time=datetime.now(timezone.utc),
                target_time=datetime.now(timezone.utc) + timedelta(hours=horizon_hours),
                metadata={'current_mentions': trend_data.get('mentions', 0)}
            )
            
        except Exception as e:
            logger.error(f"Error predicting mentions: {e}")
            return None
    
    async def _predict_viral_probability(self, model, trend_data: Dict[str, Any], 
                                       features: Dict[str, Any], horizon_hours: int) -> Optional[PredictionResult]:
        """Predict viral probability"""
        try:
            viral_prob = model.predict_viral_probability(features, horizon_hours)
            confidence = model.get_confidence(features, 'viral')
            
            return PredictionResult(
                keyword=trend_data.get('keyword', ''),
                prediction_type='viral_probability',
                predicted_value=viral_prob,
                confidence=confidence,
                horizon_hours=horizon_hours,
                model_name=model.__class__.__name__,
                features_used=features,
                prediction_time=datetime.now(timezone.utc),
                target_time=datetime.now(timezone.utc) + timedelta(hours=horizon_hours),
                metadata={'current_viral_prob': trend_data.get('viral_probability', 0)}
            )
            
        except Exception as e:
            logger.error(f"Error predicting viral probability: {e}")
            return None
    
    async def _predict_peak_time(self, model, trend_data: Dict[str, Any], 
                               features: Dict[str, Any], horizon_hours: int) -> Optional[PredictionResult]:
        """Predict when trend will peak"""
        try:
            hours_to_peak = model.predict_peak_time(features, horizon_hours)
            confidence = model.get_confidence(features, 'peak_time')
            
            return PredictionResult(
                keyword=trend_data.get('keyword', ''),
                prediction_type='peak_time',
                predicted_value=hours_to_peak,
                confidence=confidence,
                horizon_hours=horizon_hours,
                model_name=model.__class__.__name__,
                features_used=features,
                prediction_time=datetime.now(timezone.utc),
                target_time=datetime.now(timezone.utc) + timedelta(hours=hours_to_peak),
                metadata={'current_stage': trend_data.get('lifecycle_stage', 'unknown')}
            )
            
        except Exception as e:
            logger.error(f"Error predicting peak time: {e}")
            return None
    
    def _extract_features(self, trend_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract features for ML models"""
        features = {
            # Basic metrics
            'mentions': trend_data.get('mentions', 0),
            'velocity': trend_data.get('velocity', 0),
            'acceleration': trend_data.get('acceleration', 0),
            'engagement_rate': trend_data.get('engagement_rate', 0),
            'sentiment_score': trend_data.get('sentiment_score', 0),
            'geographic_spread': trend_data.get('geographic_spread', 0),
            'trend_score': trend_data.get('trend_score', 0),
            'viral_probability': trend_data.get('viral_probability', 0),
            
            # Platform features
            'platform_count': len(trend_data.get('platforms', [])),
            'primary_platform': trend_data.get('platform', 'unknown'),
            
            # Temporal features
            'hour_of_day': datetime.now(timezone.utc).hour,
            'day_of_week': datetime.now(timezone.utc).weekday(),
            'is_weekend': datetime.now(timezone.utc).weekday() >= 5,
            
            # Content features
            'keyword_length': len(trend_data.get('keyword', '')),
            'has_hashtag': trend_data.get('type') == 'hashtag',
            
            # Derived features
            'momentum': trend_data.get('velocity', 0) * trend_data.get('acceleration', 0),
            'engagement_velocity': trend_data.get('engagement_rate', 0) * trend_data.get('velocity', 0),
            'sentiment_momentum': trend_data.get('sentiment_score', 0) * trend_data.get('velocity', 0),
        }
        
        # Normalize features
        features['mentions_normalized'] = min(features['mentions'] / 1000, 1.0)
        features['velocity_normalized'] = min(abs(features['velocity']) / 100, 1.0)
        features['engagement_normalized'] = min(features['engagement_rate'] / 10, 1.0)
        
        return features
    
    async def predict_content_performance(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """Predict how well content will perform"""
        try:
            features = self._extract_content_features(content_data)
            
            # Predict engagement metrics
            predicted_likes = self._predict_engagement_metric(features, 'likes')
            predicted_shares = self._predict_engagement_metric(features, 'shares')
            predicted_comments = self._predict_engagement_metric(features, 'comments')
            
            # Predict viral potential
            viral_score = self._calculate_viral_potential(features)
            
            # Predict optimal posting time
            optimal_time = self._predict_optimal_posting_time(features)
            
            return {
                'predicted_engagement': {
                    'likes': predicted_likes,
                    'shares': predicted_shares,
                    'comments': predicted_comments,
                    'total': predicted_likes + predicted_shares + predicted_comments
                },
                'viral_potential': viral_score,
                'optimal_posting_time': optimal_time,
                'confidence': self._calculate_content_prediction_confidence(features),
                'factors': self._identify_performance_factors(features),
                'predicted_at': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error predicting content performance: {e}")
            return {}
    
    def _extract_content_features(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract features for content performance prediction"""
        text = content_data.get('text', '')
        hashtags = content_data.get('hashtags', [])
        
        return {
            'text_length': len(text),
            'word_count': len(text.split()),
            'hashtag_count': len(hashtags),
            'mention_count': len(content_data.get('mentions', [])),
            'url_count': len(content_data.get('urls', [])),
            'sentiment_score': content_data.get('sentiment_score', 0),
            'has_media': len(content_data.get('media_urls', [])) > 0,
            'author_followers': content_data.get('author_followers', 0),
            'author_verified': content_data.get('author_verified', False),
            'platform': content_data.get('platform', 'unknown'),
            'hour_of_day': datetime.now(timezone.utc).hour,
            'day_of_week': datetime.now(timezone.utc).weekday(),
            'trending_hashtags': self._count_trending_hashtags(hashtags),
            'content_type': content_data.get('content_type', 'text')
        }
    
    def _predict_engagement_metric(self, features: Dict[str, Any], metric_type: str) -> float:
        """Predict specific engagement metric"""
        base_score = 0.0
        
        # Text quality factors
        if 10 <= features.get('word_count', 0) <= 50:
            base_score += 20
        
        # Hashtag factors
        hashtag_count = features.get('hashtag_count', 0)
        if 1 <= hashtag_count <= 3:
            base_score += 15
        elif hashtag_count > 5:
            base_score -= 10
        
        # Sentiment factors
        sentiment = features.get('sentiment_score', 0)
        if sentiment > 0.3:
            base_score += 25
        elif sentiment < -0.3:
            base_score += 10  # Negative content can also drive engagement
        
        # Media factors
        if features.get('has_media', False):
            base_score += 30
        
        # Author factors
        followers = features.get('author_followers', 0)
        if followers > 1000:
            base_score += min(50, followers / 1000)
        
        if features.get('author_verified', False):
            base_score += 20
        
        # Timing factors
        hour = features.get('hour_of_day', 12)
        if 9 <= hour <= 17:  # Business hours
            base_score += 10
        elif 19 <= hour <= 22:  # Evening peak
            base_score += 15
        
        # Platform-specific adjustments
        platform = features.get('platform', 'unknown')
        platform_multipliers = {
            'twitter': 1.0,
            'instagram': 1.2,
            'youtube': 0.8,
            'reddit': 0.9
        }
        base_score *= platform_multipliers.get(platform, 1.0)
        
        # Metric-specific adjustments
        metric_multipliers = {
            'likes': 1.0,
            'shares': 0.3,
            'comments': 0.2
        }
        
        return max(0, base_score * metric_multipliers.get(metric_type, 1.0))
    
    def _calculate_viral_potential(self, features: Dict[str, Any]) -> float:
        """Calculate viral potential score"""
        viral_score = 0.0
        
        # High engagement potential
        if features.get('sentiment_score', 0) > 0.5:
            viral_score += 0.3
        
        # Media content
        if features.get('has_media', False):
            viral_score += 0.2
        
        # Trending hashtags
        viral_score += min(0.3, features.get('trending_hashtags', 0) * 0.1)
        
        # Author influence
        followers = features.get('author_followers', 0)
        if followers > 10000:
            viral_score += min(0.2, followers / 100000)
        
        return min(1.0, viral_score)
    
    def _predict_optimal_posting_time(self, features: Dict[str, Any]) -> str:
        """Predict optimal posting time"""
        platform = features.get('platform', 'unknown')
        
        # Platform-specific optimal times (UTC)
        optimal_times = {
            'twitter': ['13:00', '16:00', '19:00'],
            'instagram': ['15:00', '17:00', '21:00'],
            'youtube': ['18:00', '20:00'],
            'reddit': ['14:00', '18:00', '22:00']
        }
        
        times = optimal_times.get(platform, ['12:00', '18:00'])
        
        # Consider current time and suggest next optimal time
        current_hour = datetime.now(timezone.utc).hour
        for time_str in times:
            time_hour = int(time_str.split(':')[0])
            if time_hour > current_hour:
                return time_str
        
        # If all times have passed, suggest first time tomorrow
        return times[0]
    
    def _calculate_content_prediction_confidence(self, features: Dict[str, Any]) -> float:
        """Calculate confidence in content predictions"""
        confidence = 0.5  # Base confidence
        
        # More data = higher confidence
        if features.get('author_followers', 0) > 1000:
            confidence += 0.2
        
        if features.get('hashtag_count', 0) > 0:
            confidence += 0.1
        
        if features.get('has_media', False):
            confidence += 0.1
        
        if features.get('word_count', 0) > 10:
            confidence += 0.1
        
        return min(1.0, confidence)
    
    def _identify_performance_factors(self, features: Dict[str, Any]) -> List[str]:
        """Identify key factors affecting performance"""
        factors = []
        
        if features.get('sentiment_score', 0) > 0.3:
            factors.append('positive_sentiment')
        
        if features.get('has_media', False):
            factors.append('media_content')
        
        if features.get('author_verified', False):
            factors.append('verified_author')
        
        if features.get('trending_hashtags', 0) > 0:
            factors.append('trending_hashtags')
        
        if features.get('author_followers', 0) > 10000:
            factors.append('large_audience')
        
        return factors
    
    def _count_trending_hashtags(self, hashtags: List[str]) -> int:
        """Count how many hashtags are currently trending"""
        # Simplified - in production, check against current trending hashtags
        trending_keywords = ['ai', 'tech', 'viral', 'trending', 'breaking', 'news']
        return sum(1 for hashtag in hashtags if hashtag.lower() in trending_keywords)
    
    async def batch_predict(self, trends: List[Dict[str, Any]], 
                          horizon_hours: int = 24) -> List[Dict[str, Any]]:
        """Predict future for multiple trends"""
        results = []
        
        for trend in trends:
            try:
                predictions = await self.predict_trend_future(trend, horizon_hours)
                
                trend_result = {
                    'keyword': trend.get('keyword', ''),
                    'current_metrics': trend,
                    'predictions': [pred.__dict__ for pred in predictions],
                    'prediction_summary': self._summarize_predictions(predictions)
                }
                results.append(trend_result)
                
            except Exception as e:
                logger.error(f"Error predicting trend {trend.get('keyword', 'unknown')}: {e}")
                continue
        
        return results
    
    def _summarize_predictions(self, predictions: List[PredictionResult]) -> Dict[str, Any]:
        """Summarize predictions for a trend"""
        if not predictions:
            return {}
        
        summary = {
            'total_predictions': len(predictions),
            'average_confidence': statistics.mean([p.confidence for p in predictions]),
            'prediction_types': list(set([p.prediction_type for p in predictions])),
            'models_used': list(set([p.model_name for p in predictions]))
        }
        
        # Get specific prediction values
        for pred in predictions:
            if pred.prediction_type == 'mentions':
                summary['predicted_mentions'] = pred.predicted_value
            elif pred.prediction_type == 'viral_probability':
                summary['predicted_viral_prob'] = pred.predicted_value
            elif pred.prediction_type == 'peak_time':
                summary['predicted_peak_hours'] = pred.predicted_value
        
        return summary


# Simple ML Models (no external dependencies)

class LinearTrendModel:
    """Simple linear trend extrapolation"""
    
    def predict_mentions(self, features: Dict[str, Any], horizon_hours: int) -> float:
        current_mentions = features.get('mentions', 0)
        velocity = features.get('velocity', 0)
        
        # Linear extrapolation
        predicted_mentions = current_mentions + (velocity * horizon_hours)
        return max(0, predicted_mentions)
    
    def get_confidence(self, features: Dict[str, Any], prediction_type: str) -> float:
        # Simple confidence based on data quality
        base_confidence = 0.6
        
        if features.get('mentions', 0) > 50:
            base_confidence += 0.2
        
        if abs(features.get('velocity', 0)) > 5:
            base_confidence += 0.1
        
        return min(1.0, base_confidence)


class ExponentialGrowthModel:
    """Exponential growth model for viral trends"""
    
    def predict_mentions(self, features: Dict[str, Any], horizon_hours: int) -> float:
        current_mentions = features.get('mentions', 0)
        acceleration = features.get('acceleration', 0)
        
        if acceleration <= 0:
            return current_mentions
        
        # Exponential growth with decay
        growth_rate = min(0.1, acceleration / 10)  # Cap growth rate
        predicted_mentions = current_mentions * (1 + growth_rate) ** horizon_hours
        
        return min(predicted_mentions, current_mentions * 10)  # Cap at 10x growth
    
    def predict_viral_probability(self, features: Dict[str, Any], horizon_hours: int) -> float:
        current_viral_prob = features.get('viral_probability', 0)
        acceleration = features.get('acceleration', 0)
        engagement_rate = features.get('engagement_rate', 0)
        
        # Increase viral probability based on acceleration and engagement
        viral_boost = (acceleration * 0.1) + (engagement_rate * 0.05)
        predicted_viral_prob = current_viral_prob + viral_boost
        
        return min(1.0, max(0.0, predicted_viral_prob))
    
    def get_confidence(self, features: Dict[str, Any], prediction_type: str) -> float:
        return 0.7 if features.get('acceleration', 0) > 0 else 0.4


class ViralClassifier:
    """Classifier for viral content prediction"""
    
    def predict_viral_probability(self, features: Dict[str, Any], horizon_hours: int) -> float:
        score = 0.0
        
        # Engagement factors
        if features.get('engagement_rate', 0) > 5:
            score += 0.3
        
        # Velocity factors
        if features.get('velocity', 0) > 20:
            score += 0.3
        
        # Platform factors
        if features.get('platform_count', 0) > 2:
            score += 0.2
        
        # Sentiment factors
        if abs(features.get('sentiment_score', 0)) > 0.5:
            score += 0.2
        
        return min(1.0, score)
    
    def get_confidence(self, features: Dict[str, Any], prediction_type: str) -> float:
        return 0.8


class PeakPredictor:
    """Predicts when trends will reach their peak"""
    
    def predict_peak_time(self, features: Dict[str, Any], horizon_hours: int) -> float:
        velocity = features.get('velocity', 0)
        acceleration = features.get('acceleration', 0)
        
        if acceleration <= 0 or velocity <= 0:
            return horizon_hours  # Already peaked or declining
        
        # Simple physics: time to peak = velocity / acceleration
        hours_to_peak = velocity / acceleration
        
        # Clamp to reasonable range
        return max(1, min(hours_to_peak, horizon_hours))
    
    def get_confidence(self, features: Dict[str, Any], prediction_type: str) -> float:
        if features.get('acceleration', 0) > 0:
            return 0.7
        return 0.3


class LifecyclePredictor:
    """Predicts trend lifecycle transitions"""
    
    def predict_lifecycle_stage(self, features: Dict[str, Any], horizon_hours: int) -> str:
        velocity = features.get('velocity', 0)
        acceleration = features.get('acceleration', 0)
        trend_score = features.get('trend_score', 0)
        
        if trend_score < 0.3:
            return TrendStage.EMERGING.value
        elif velocity > 0 and acceleration > 0:
            return TrendStage.GROWING.value
        elif velocity > 0 and acceleration <= 0:
            return TrendStage.PEAK.value
        else:
            return TrendStage.DECLINING.value
    
    def get_confidence(self, features: Dict[str, Any], prediction_type: str) -> float:
        return 0.6
