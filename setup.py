"""
Setup script for TrendRadar
Social Media Trend Prediction & Early Warning System
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="trendradar",
    version="1.0.0",
    author="HectorTa1989",
    author_email="<EMAIL>",
    description="Advanced Social Media Trend Prediction & Early Warning System",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/HectorTa1989/TrendRadar",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
            "pre-commit>=3.0.0",
        ],
        "docs": [
            "sphinx>=5.0.0",
            "sphinx-rtd-theme>=1.0.0",
            "sphinxcontrib-asyncio>=0.3.0",
        ],
        "monitoring": [
            "prometheus-client>=0.19.0",
            "grafana-api>=1.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "trendradar=src.main:main",
            "trendradar-setup=scripts.setup_database:main",
            "trendradar-migrate=scripts.data_migration:main",
        ],
    },
    include_package_data=True,
    package_data={
        "trendradar": [
            "config/*.yaml",
            "database/schemas/*.sql",
            "frontend/build/*",
        ],
    },
    zip_safe=False,
    keywords=[
        "social media",
        "trend analysis",
        "prediction",
        "machine learning",
        "twitter",
        "reddit",
        "youtube",
        "instagram",
        "analytics",
        "monitoring",
        "alerts",
        "forecasting"
    ],
    project_urls={
        "Bug Reports": "https://github.com/HectorTa1989/TrendRadar/issues",
        "Source": "https://github.com/HectorTa1989/TrendRadar",
        "Documentation": "https://github.com/HectorTa1989/TrendRadar/wiki",
    },
)
