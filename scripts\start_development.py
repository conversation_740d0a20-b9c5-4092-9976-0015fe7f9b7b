#!/usr/bin/env python3
"""
Development Environment Startup Script for TrendRadar
Sets up and starts the development environment with all necessary services
"""

import asyncio
import logging
import sys
import os
import subprocess
import time
from pathlib import Path
from typing import Dict, Any, List
import requests
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from config.database import init_database, engine
from src.main import app
import click

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DevelopmentEnvironment:
    """Manages the development environment setup and startup"""
    
    def __init__(self):
        self.project_root = project_root
        self.services_status = {}
        self.required_services = ['database', 'redis', 'api']
        
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are installed"""
        logger.info("Checking prerequisites...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            logger.error("Python 3.8 or higher is required")
            return False
        
        # Check if virtual environment is activated
        if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            logger.warning("Virtual environment not detected. Consider using a virtual environment.")
        
        # Check required files
        required_files = [
            '.env',
            'requirements.txt',
            'src/main.py',
            'config/settings.py'
        ]
        
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                logger.error(f"Required file missing: {file_path}")
                return False
        
        logger.info("Prerequisites check passed")
        return True
    
    def setup_environment(self) -> bool:
        """Setup the development environment"""
        logger.info("Setting up development environment...")
        
        try:
            # Set environment variables
            os.environ['ENVIRONMENT'] = 'development'
            os.environ['DEBUG'] = 'True'
            os.environ['LOG_LEVEL'] = 'DEBUG'
            
            # Create necessary directories
            directories = [
                'logs',
                'data',
                'models',
                'temp'
            ]
            
            for directory in directories:
                dir_path = self.project_root / directory
                dir_path.mkdir(exist_ok=True)
                logger.info(f"Created directory: {directory}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting up environment: {e}")
            return False
    
    def check_database(self) -> bool:
        """Check database connectivity"""
        logger.info("Checking database connectivity...")
        
        try:
            # Initialize database
            init_database()
            
            # Test connection
            with engine.connect() as connection:
                result = connection.execute("SELECT 1")
                if result.fetchone():
                    logger.info("Database connection successful")
                    self.services_status['database'] = 'healthy'
                    return True
            
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            self.services_status['database'] = 'unhealthy'
            return False
    
    def check_redis(self) -> bool:
        """Check Redis connectivity"""
        logger.info("Checking Redis connectivity...")
        
        try:
            import redis
            
            # Parse Redis URL
            redis_url = settings.redis_url
            if redis_url:
                r = redis.from_url(redis_url)
                r.ping()
                logger.info("Redis connection successful")
                self.services_status['redis'] = 'healthy'
                return True
            else:
                logger.warning("Redis URL not configured, skipping Redis check")
                self.services_status['redis'] = 'not_configured'
                return True
                
        except ImportError:
            logger.warning("Redis not installed, skipping Redis check")
            self.services_status['redis'] = 'not_installed'
            return True
        except Exception as e:
            logger.error(f"Redis connection failed: {e}")
            self.services_status['redis'] = 'unhealthy'
            return False
    
    def install_dependencies(self) -> bool:
        """Install Python dependencies"""
        logger.info("Installing dependencies...")
        
        try:
            # Install requirements
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ], cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("Dependencies installed successfully")
                return True
            else:
                logger.error(f"Failed to install dependencies: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error installing dependencies: {e}")
            return False
    
    def setup_database(self) -> bool:
        """Setup database schema"""
        logger.info("Setting up database schema...")
        
        try:
            # Run database setup script
            setup_script = self.project_root / 'scripts' / 'setup_database.py'
            
            result = subprocess.run([
                sys.executable, str(setup_script)
            ], cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("Database schema setup completed")
                return True
            else:
                logger.error(f"Database setup failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error setting up database: {e}")
            return False
    
    def start_api_server(self, host: str = "0.0.0.0", port: int = 8000) -> bool:
        """Start the API server"""
        logger.info(f"Starting API server on {host}:{port}...")
        
        try:
            import uvicorn
            
            # Start server in development mode
            uvicorn.run(
                "src.main:app",
                host=host,
                port=port,
                reload=True,
                log_level="debug",
                access_log=True
            )
            
            return True
            
        except ImportError:
            logger.error("uvicorn not installed. Install with: pip install uvicorn")
            return False
        except Exception as e:
            logger.error(f"Error starting API server: {e}")
            return False
    
    def verify_api_endpoints(self, base_url: str = "http://localhost:8000") -> bool:
        """Verify that API endpoints are working"""
        logger.info("Verifying API endpoints...")
        
        endpoints_to_test = [
            "/health",
            "/api/v1/trends",
            "/api/v1/predictions",
            "/api/v1/platforms/status",
            "/docs"
        ]
        
        success_count = 0
        
        for endpoint in endpoints_to_test:
            try:
                url = f"{base_url}{endpoint}"
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    logger.info(f"✓ {endpoint} - OK")
                    success_count += 1
                else:
                    logger.warning(f"✗ {endpoint} - Status: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"✗ {endpoint} - Error: {e}")
        
        success_rate = success_count / len(endpoints_to_test)
        logger.info(f"API verification: {success_count}/{len(endpoints_to_test)} endpoints working ({success_rate:.1%})")
        
        return success_rate >= 0.8  # 80% success rate required
    
    def test_data_collection(self) -> bool:
        """Test data collection from social media platforms"""
        logger.info("Testing data collection...")
        
        try:
            from src.data_collection.base_collector import DataCollectionManager
            from src.utils.constants import Platform
            
            # Initialize data collection manager
            manager = DataCollectionManager()
            
            # Test each platform (with mock data if API keys not available)
            platforms_tested = 0
            platforms_working = 0
            
            for platform in Platform:
                try:
                    logger.info(f"Testing {platform.value} data collection...")
                    
                    # This would test actual collection in a real environment
                    # For development, we'll just check if the collector can be initialized
                    collector = manager.get_collector(platform)
                    if collector:
                        platforms_working += 1
                        logger.info(f"✓ {platform.value} collector initialized")
                    else:
                        logger.warning(f"✗ {platform.value} collector not available")
                    
                    platforms_tested += 1
                    
                except Exception as e:
                    logger.error(f"✗ {platform.value} collector error: {e}")
                    platforms_tested += 1
            
            success_rate = platforms_working / platforms_tested if platforms_tested > 0 else 0
            logger.info(f"Data collection test: {platforms_working}/{platforms_tested} platforms working ({success_rate:.1%})")
            
            return success_rate >= 0.5  # 50% success rate required
            
        except Exception as e:
            logger.error(f"Error testing data collection: {e}")
            return False
    
    def print_status_report(self):
        """Print a comprehensive status report"""
        logger.info("\n" + "="*60)
        logger.info("TRENDRADAR DEVELOPMENT ENVIRONMENT STATUS")
        logger.info("="*60)
        
        for service, status in self.services_status.items():
            status_icon = "✓" if status == "healthy" else "✗" if status == "unhealthy" else "⚠"
            logger.info(f"{status_icon} {service.upper()}: {status}")
        
        logger.info("\nAPI Endpoints:")
        logger.info("- Health Check: http://localhost:8000/health")
        logger.info("- API Documentation: http://localhost:8000/docs")
        logger.info("- Trends API: http://localhost:8000/api/v1/trends")
        logger.info("- Predictions API: http://localhost:8000/api/v1/predictions")
        
        logger.info("\nNext Steps:")
        logger.info("1. Visit http://localhost:8000/docs to explore the API")
        logger.info("2. Configure API keys in .env file for data collection")
        logger.info("3. Run tests with: pytest tests/")
        logger.info("4. Check logs in the logs/ directory")
        
        logger.info("="*60)


@click.command()
@click.option('--skip-deps', is_flag=True, help='Skip dependency installation')
@click.option('--skip-db', is_flag=True, help='Skip database setup')
@click.option('--host', default='0.0.0.0', help='Host to bind the server to')
@click.option('--port', default=8000, help='Port to bind the server to')
@click.option('--verify-only', is_flag=True, help='Only verify the setup without starting server')
def main(skip_deps, skip_db, host, port, verify_only):
    """Start TrendRadar development environment"""
    
    env = DevelopmentEnvironment()
    
    logger.info("Starting TrendRadar Development Environment Setup...")
    
    # Check prerequisites
    if not env.check_prerequisites():
        logger.error("Prerequisites check failed. Please fix the issues and try again.")
        sys.exit(1)
    
    # Setup environment
    if not env.setup_environment():
        logger.error("Environment setup failed.")
        sys.exit(1)
    
    # Install dependencies
    if not skip_deps:
        if not env.install_dependencies():
            logger.error("Dependency installation failed.")
            sys.exit(1)
    
    # Setup database
    if not skip_db:
        if not env.setup_database():
            logger.error("Database setup failed.")
            sys.exit(1)
    
    # Check services
    env.check_database()
    env.check_redis()
    
    if verify_only:
        # Just verify and exit
        env.print_status_report()
        
        # Test API in background
        logger.info("Starting temporary server for verification...")
        import threading
        import uvicorn
        
        def run_server():
            uvicorn.run("src.main:app", host=host, port=port, log_level="error")
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # Wait for server to start
        time.sleep(3)
        
        # Verify endpoints
        if env.verify_api_endpoints(f"http://{host}:{port}"):
            logger.info("✓ API verification successful")
        else:
            logger.warning("⚠ Some API endpoints failed verification")
        
        # Test data collection
        if env.test_data_collection():
            logger.info("✓ Data collection test successful")
        else:
            logger.warning("⚠ Data collection test failed")
        
        env.print_status_report()
        return
    
    # Print status before starting
    env.print_status_report()
    
    # Start the API server
    logger.info("Starting TrendRadar API server...")
    logger.info("Press Ctrl+C to stop the server")
    
    try:
        env.start_api_server(host, port)
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
