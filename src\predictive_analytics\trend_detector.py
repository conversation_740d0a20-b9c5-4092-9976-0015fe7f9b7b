"""
Trend Detection Engine for TrendRadar
Advanced algorithms for detecting emerging trends and trend patterns
"""

import asyncio
import logging
import numpy as np
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque
import statistics

from src.utils.helpers import calculate_velocity, calculate_acceleration, weighted_average, get_time_bucket
from src.utils.constants import TrendStage, Platform, TREND_THRESHOLDS, TIME_WINDOWS, SCORING_WEIGHTS
from config.database import get_async_db, CacheManager

logger = logging.getLogger(__name__)


@dataclass
class TrendSignal:
    """Represents a trend signal with metrics"""
    keyword: str
    platform: Platform
    mentions: int
    velocity: float
    acceleration: float
    engagement_rate: float
    geographic_spread: int
    sentiment_score: float
    confidence: float
    timestamp: datetime
    stage: TrendStage = TrendStage.EMERGING


@dataclass
class TrendPattern:
    """Represents a detected trend pattern"""
    id: str
    keyword: str
    platforms: List[Platform]
    signals: List[TrendSignal]
    trend_score: float
    viral_probability: float
    lifecycle_stage: TrendStage
    peak_prediction: Optional[datetime]
    geographic_data: Dict[str, Any]
    created_at: datetime
    updated_at: datetime


class TrendDetector:
    """Main trend detection engine"""
    
    def __init__(self):
        self.cache_manager = CacheManager()
        self.signal_buffer = defaultdict(lambda: deque(maxlen=100))  # Store recent signals
        self.trend_patterns = {}
        self.detection_thresholds = TREND_THRESHOLDS
        self.time_windows = TIME_WINDOWS
        
    async def detect_trends(self, content_data: List[Dict[str, Any]]) -> List[TrendPattern]:
        """Main trend detection method"""
        try:
            # Extract signals from content data
            signals = await self._extract_signals(content_data)
            
            # Analyze signals for trend patterns
            trend_patterns = await self._analyze_signals(signals)
            
            # Filter and rank trends
            filtered_trends = await self._filter_and_rank_trends(trend_patterns)
            
            # Update trend lifecycle stages
            updated_trends = await self._update_trend_stages(filtered_trends)
            
            logger.info(f"Detected {len(updated_trends)} trend patterns")
            return updated_trends
            
        except Exception as e:
            logger.error(f"Error in trend detection: {e}")
            return []
    
    async def _extract_signals(self, content_data: List[Dict[str, Any]]) -> List[TrendSignal]:
        """Extract trend signals from content data"""
        signals = []
        keyword_metrics = defaultdict(lambda: {
            'mentions': 0,
            'total_engagement': 0,
            'platforms': set(),
            'locations': set(),
            'timestamps': [],
            'sentiment_scores': []
        })
        
        # Aggregate data by keyword
        for content in content_data:
            hashtags = content.get('hashtags', [])
            platform = Platform(content.get('platform', 'twitter'))
            engagement = content.get('engagement_metrics', {})
            location = content.get('location')
            timestamp = content.get('created_at', datetime.now(timezone.utc))
            sentiment = content.get('sentiment_score', 0.0)
            
            total_engagement = sum(engagement.values()) if engagement else 0
            
            for hashtag in hashtags:
                keyword_metrics[hashtag]['mentions'] += 1
                keyword_metrics[hashtag]['total_engagement'] += total_engagement
                keyword_metrics[hashtag]['platforms'].add(platform)
                if location:
                    keyword_metrics[hashtag]['locations'].add(location)
                keyword_metrics[hashtag]['timestamps'].append(timestamp)
                keyword_metrics[hashtag]['sentiment_scores'].append(sentiment)
        
        # Create signals for keywords with sufficient activity
        for keyword, metrics in keyword_metrics.items():
            if metrics['mentions'] >= self.detection_thresholds['min_mentions']:
                signal = await self._create_trend_signal(keyword, metrics)
                if signal:
                    signals.append(signal)
        
        return signals
    
    async def _create_trend_signal(self, keyword: str, metrics: Dict[str, Any]) -> Optional[TrendSignal]:
        """Create a trend signal from aggregated metrics"""
        try:
            mentions = metrics['mentions']
            platforms = list(metrics['platforms'])
            timestamps = sorted(metrics['timestamps'])
            
            # Calculate velocity and acceleration
            velocity = await self._calculate_velocity(keyword, mentions, timestamps)
            acceleration = await self._calculate_acceleration(keyword, velocity)
            
            # Calculate engagement rate
            engagement_rate = (
                metrics['total_engagement'] / mentions 
                if mentions > 0 else 0
            )
            
            # Calculate geographic spread
            geographic_spread = len(metrics['locations'])
            
            # Calculate sentiment score
            sentiment_scores = metrics['sentiment_scores']
            sentiment_score = statistics.mean(sentiment_scores) if sentiment_scores else 0.0
            
            # Calculate confidence based on data quality
            confidence = self._calculate_signal_confidence(
                mentions, len(platforms), geographic_spread, len(timestamps)
            )
            
            # Determine primary platform (most mentions)
            platform_counts = defaultdict(int)
            # This would need to be calculated from the original data
            primary_platform = platforms[0] if platforms else Platform.TWITTER
            
            return TrendSignal(
                keyword=keyword,
                platform=primary_platform,
                mentions=mentions,
                velocity=velocity,
                acceleration=acceleration,
                engagement_rate=engagement_rate,
                geographic_spread=geographic_spread,
                sentiment_score=sentiment_score,
                confidence=confidence,
                timestamp=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"Error creating trend signal for {keyword}: {e}")
            return None
    
    async def _calculate_velocity(self, keyword: str, current_mentions: int, timestamps: List[datetime]) -> float:
        """Calculate trend velocity (mentions per hour)"""
        try:
            # Get historical data from cache
            cache_key = f"trend_history:{keyword}"
            historical_data = await self.cache_manager.get(cache_key)
            
            if not historical_data:
                # No historical data, return current rate
                if len(timestamps) > 1:
                    time_span = (timestamps[-1] - timestamps[0]).total_seconds() / 3600
                    return current_mentions / max(time_span, 1)
                return 0.0
            
            # Calculate velocity based on change over time window
            window_minutes = self.time_windows['velocity_minutes']
            cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=window_minutes)
            
            recent_timestamps = [t for t in timestamps if t >= cutoff_time]
            recent_mentions = len(recent_timestamps)
            
            time_span_hours = window_minutes / 60
            velocity = recent_mentions / time_span_hours
            
            return velocity
            
        except Exception as e:
            logger.error(f"Error calculating velocity for {keyword}: {e}")
            return 0.0
    
    async def _calculate_acceleration(self, keyword: str, current_velocity: float) -> float:
        """Calculate trend acceleration (change in velocity)"""
        try:
            # Get previous velocity from buffer
            signal_history = self.signal_buffer[keyword]
            
            if len(signal_history) < 2:
                return 0.0
            
            previous_velocity = signal_history[-1].velocity if signal_history else 0.0
            time_interval = self.time_windows['acceleration_minutes'] / 60  # Convert to hours
            
            acceleration = (current_velocity - previous_velocity) / time_interval
            return acceleration
            
        except Exception as e:
            logger.error(f"Error calculating acceleration for {keyword}: {e}")
            return 0.0
    
    def _calculate_signal_confidence(self, mentions: int, platform_count: int, 
                                   geo_spread: int, timestamp_count: int) -> float:
        """Calculate confidence score for a trend signal"""
        try:
            # Normalize factors
            mention_score = min(mentions / 100, 1.0)  # Cap at 100 mentions
            platform_score = min(platform_count / 4, 1.0)  # Cap at 4 platforms
            geo_score = min(geo_spread / 10, 1.0)  # Cap at 10 locations
            time_score = min(timestamp_count / 50, 1.0)  # Cap at 50 data points
            
            # Weighted confidence calculation
            weights = [0.4, 0.25, 0.2, 0.15]  # mentions, platforms, geo, time
            scores = [mention_score, platform_score, geo_score, time_score]
            
            confidence = weighted_average(scores, weights)
            return confidence
            
        except Exception as e:
            logger.error(f"Error calculating signal confidence: {e}")
            return 0.0
    
    async def _analyze_signals(self, signals: List[TrendSignal]) -> List[TrendPattern]:
        """Analyze signals to identify trend patterns"""
        try:
            trend_patterns = []
            
            # Group signals by keyword
            keyword_signals = defaultdict(list)
            for signal in signals:
                keyword_signals[signal.keyword].append(signal)
            
            # Analyze each keyword's signals
            for keyword, keyword_signal_list in keyword_signals.items():
                pattern = await self._create_trend_pattern(keyword, keyword_signal_list)
                if pattern:
                    trend_patterns.append(pattern)
            
            return trend_patterns
            
        except Exception as e:
            logger.error(f"Error analyzing signals: {e}")
            return []
    
    async def _create_trend_pattern(self, keyword: str, signals: List[TrendSignal]) -> Optional[TrendPattern]:
        """Create a trend pattern from signals"""
        try:
            if not signals:
                return None
            
            # Aggregate signal data
            platforms = list(set(signal.platform for signal in signals))
            total_mentions = sum(signal.mentions for signal in signals)
            avg_velocity = statistics.mean(signal.velocity for signal in signals)
            avg_acceleration = statistics.mean(signal.acceleration for signal in signals)
            avg_engagement = statistics.mean(signal.engagement_rate for signal in signals)
            max_geo_spread = max(signal.geographic_spread for signal in signals)
            avg_sentiment = statistics.mean(signal.sentiment_score for signal in signals)
            avg_confidence = statistics.mean(signal.confidence for signal in signals)
            
            # Calculate trend score
            trend_score = self._calculate_trend_score(
                total_mentions, avg_velocity, avg_acceleration, 
                avg_engagement, max_geo_spread
            )
            
            # Calculate viral probability
            viral_probability = self._calculate_viral_probability(
                avg_engagement, avg_velocity, len(platforms), 
                max_geo_spread, avg_sentiment
            )
            
            # Determine lifecycle stage
            lifecycle_stage = self._determine_lifecycle_stage(
                avg_velocity, avg_acceleration, trend_score
            )
            
            # Predict peak time
            peak_prediction = self._predict_peak_time(
                avg_velocity, avg_acceleration, lifecycle_stage
            )
            
            # Create geographic data summary
            geographic_data = {
                'spread_count': max_geo_spread,
                'platforms': [p.value for p in platforms],
                'sentiment': avg_sentiment
            }
            
            pattern_id = f"{keyword}_{int(datetime.now(timezone.utc).timestamp())}"
            
            return TrendPattern(
                id=pattern_id,
                keyword=keyword,
                platforms=platforms,
                signals=signals,
                trend_score=trend_score,
                viral_probability=viral_probability,
                lifecycle_stage=lifecycle_stage,
                peak_prediction=peak_prediction,
                geographic_data=geographic_data,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"Error creating trend pattern for {keyword}: {e}")
            return None
    
    def _calculate_trend_score(self, mentions: int, velocity: float, acceleration: float,
                              engagement_rate: float, geographic_spread: int) -> float:
        """Calculate comprehensive trend score"""
        try:
            # Normalize components
            mention_score = min(mentions / 1000, 1.0)
            velocity_score = min(abs(velocity) / 100, 1.0)
            acceleration_score = min(abs(acceleration) / 10, 1.0)
            engagement_score = min(engagement_rate / 10, 1.0)
            geo_score = min(geographic_spread / 10, 1.0)
            
            # Use predefined weights
            weights = list(SCORING_WEIGHTS['trend_score'].values())
            scores = [mention_score, velocity_score, acceleration_score, engagement_score, geo_score]
            
            return weighted_average(scores, weights)
            
        except Exception as e:
            logger.error(f"Error calculating trend score: {e}")
            return 0.0
    
    def _calculate_viral_probability(self, engagement_rate: float, velocity: float,
                                   platform_count: int, geo_spread: int, sentiment: float) -> float:
        """Calculate probability of content going viral"""
        try:
            # Normalize factors
            engagement_score = min(engagement_rate / 10, 1.0)
            velocity_score = min(velocity / 100, 1.0)
            platform_score = min(platform_count / 4, 1.0)
            geo_score = min(geo_spread / 10, 1.0)
            sentiment_score = (sentiment + 1) / 2  # Convert from [-1,1] to [0,1]
            
            # Use predefined weights
            weights = list(SCORING_WEIGHTS['viral_prediction'].values())
            scores = [engagement_score, velocity_score, platform_score, geo_score, sentiment_score]
            
            return weighted_average(scores, weights)
            
        except Exception as e:
            logger.error(f"Error calculating viral probability: {e}")
            return 0.0
    
    def _determine_lifecycle_stage(self, velocity: float, acceleration: float, trend_score: float) -> TrendStage:
        """Determine the current lifecycle stage of a trend"""
        try:
            if trend_score < 0.3:
                return TrendStage.EMERGING
            elif velocity > 0 and acceleration > 0:
                return TrendStage.GROWING
            elif velocity > 0 and acceleration <= 0:
                return TrendStage.PEAK
            elif velocity <= 0:
                return TrendStage.DECLINING
            else:
                return TrendStage.EMERGING
                
        except Exception as e:
            logger.error(f"Error determining lifecycle stage: {e}")
            return TrendStage.EMERGING
    
    def _predict_peak_time(self, velocity: float, acceleration: float, stage: TrendStage) -> Optional[datetime]:
        """Predict when a trend will reach its peak"""
        try:
            if stage in [TrendStage.PEAK, TrendStage.DECLINING, TrendStage.DEAD]:
                return None
            
            if acceleration <= 0:
                return None
            
            # Simple prediction: time to peak = velocity / acceleration
            hours_to_peak = velocity / acceleration if acceleration > 0 else 24
            hours_to_peak = max(1, min(hours_to_peak, 168))  # Clamp between 1 hour and 1 week
            
            peak_time = datetime.now(timezone.utc) + timedelta(hours=hours_to_peak)
            return peak_time
            
        except Exception as e:
            logger.error(f"Error predicting peak time: {e}")
            return None
    
    async def _filter_and_rank_trends(self, trend_patterns: List[TrendPattern]) -> List[TrendPattern]:
        """Filter and rank trend patterns by relevance and quality"""
        try:
            # Filter trends that meet minimum thresholds
            filtered_trends = []
            
            for pattern in trend_patterns:
                if (pattern.trend_score >= self.detection_thresholds['viral_threshold'] * 0.5 and
                    pattern.viral_probability >= 0.1 and
                    len(pattern.signals) > 0):
                    filtered_trends.append(pattern)
            
            # Sort by trend score (descending)
            filtered_trends.sort(key=lambda x: x.trend_score, reverse=True)
            
            return filtered_trends
            
        except Exception as e:
            logger.error(f"Error filtering and ranking trends: {e}")
            return trend_patterns
    
    async def _update_trend_stages(self, trend_patterns: List[TrendPattern]) -> List[TrendPattern]:
        """Update trend lifecycle stages based on historical data"""
        try:
            updated_patterns = []
            
            for pattern in trend_patterns:
                # Update signal buffer
                for signal in pattern.signals:
                    self.signal_buffer[pattern.keyword].append(signal)
                
                # Store in cache for historical analysis
                cache_key = f"trend_pattern:{pattern.keyword}"
                await self.cache_manager.set(
                    cache_key, 
                    str(pattern.trend_score), 
                    ttl=3600
                )
                
                updated_patterns.append(pattern)
            
            return updated_patterns
            
        except Exception as e:
            logger.error(f"Error updating trend stages: {e}")
            return trend_patterns
    
    async def get_trend_history(self, keyword: str, days: int = 7) -> List[TrendSignal]:
        """Get historical trend data for a keyword"""
        try:
            # This would query the database for historical trend data
            # For now, return from signal buffer
            return list(self.signal_buffer.get(keyword, []))
            
        except Exception as e:
            logger.error(f"Error getting trend history for {keyword}: {e}")
            return []
    
    async def predict_trend_trajectory(self, pattern: TrendPattern) -> Dict[str, Any]:
        """Predict the future trajectory of a trend"""
        try:
            current_velocity = statistics.mean(s.velocity for s in pattern.signals)
            current_acceleration = statistics.mean(s.acceleration for s in pattern.signals)
            
            # Simple trajectory prediction
            trajectory = {
                'current_stage': pattern.lifecycle_stage.value,
                'predicted_peak': pattern.peak_prediction.isoformat() if pattern.peak_prediction else None,
                'growth_rate': current_velocity,
                'acceleration': current_acceleration,
                'confidence': statistics.mean(s.confidence for s in pattern.signals),
                'risk_factors': self._identify_risk_factors(pattern)
            }
            
            return trajectory
            
        except Exception as e:
            logger.error(f"Error predicting trajectory for {pattern.keyword}: {e}")
            return {}
    
    def _identify_risk_factors(self, pattern: TrendPattern) -> List[str]:
        """Identify risk factors that might affect trend development"""
        risk_factors = []
        
        try:
            avg_sentiment = statistics.mean(s.sentiment_score for s in pattern.signals)
            if avg_sentiment < -0.3:
                risk_factors.append("negative_sentiment")
            
            if pattern.viral_probability < 0.3:
                risk_factors.append("low_viral_potential")
            
            if len(pattern.platforms) == 1:
                risk_factors.append("single_platform_dependency")
            
            avg_geo_spread = statistics.mean(s.geographic_spread for s in pattern.signals)
            if avg_geo_spread < 2:
                risk_factors.append("limited_geographic_spread")
            
            return risk_factors
            
        except Exception as e:
            logger.error(f"Error identifying risk factors: {e}")
            return []
