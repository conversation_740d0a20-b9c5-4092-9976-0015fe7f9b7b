"""
Trend Extraction for TrendRadar
Extracts trending patterns and signals from processed content
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from collections import defaultdict, Counter
import statistics
import numpy as np

from database.models.content import Content, Hashtag
from database.models.trend import Trend, TrendHistory
from src.utils.helpers import calculate_velocity, calculate_acceleration, get_time_bucket
from src.utils.constants import TrendStage, Platform, TREND_THRESHOLDS, TIME_WINDOWS
from config.database import get_async_db

logger = logging.getLogger(__name__)


class TrendExtractor:
    """Extracts and analyzes trending patterns from content data"""
    
    def __init__(self):
        self.trend_thresholds = TREND_THRESHOLDS
        self.time_windows = TIME_WINDOWS
        self.min_mentions = self.trend_thresholds['min_mentions']
        self.viral_threshold = self.trend_thresholds['viral_threshold']
        
    async def extract_trends(self, content_batch: List[Content], 
                           time_window_hours: int = 24) -> List[Dict[str, Any]]:
        """Extract trending patterns from content batch"""
        try:
            # Group content by time buckets
            time_buckets = self._group_by_time_buckets(content_batch, bucket_size_minutes=60)
            
            # Extract hashtag trends
            hashtag_trends = await self._extract_hashtag_trends(content_batch, time_window_hours)
            
            # Extract keyword trends
            keyword_trends = await self._extract_keyword_trends(content_batch, time_window_hours)
            
            # Extract platform-specific trends
            platform_trends = await self._extract_platform_trends(content_batch, time_window_hours)
            
            # Combine and rank trends
            all_trends = hashtag_trends + keyword_trends + platform_trends
            ranked_trends = self._rank_trends(all_trends)
            
            logger.info(f"Extracted {len(ranked_trends)} trends from {len(content_batch)} content items")
            return ranked_trends
            
        except Exception as e:
            logger.error(f"Error extracting trends: {e}")
            return []
    
    def _group_by_time_buckets(self, content_batch: List[Content], 
                              bucket_size_minutes: int = 60) -> Dict[datetime, List[Content]]:
        """Group content by time buckets for temporal analysis"""
        buckets = defaultdict(list)
        
        for content in content_batch:
            if content.created_at:
                bucket_time = get_time_bucket(content.created_at, bucket_size_minutes)
                buckets[bucket_time].append(content)
        
        return dict(buckets)
    
    async def _extract_hashtag_trends(self, content_batch: List[Content], 
                                    time_window_hours: int) -> List[Dict[str, Any]]:
        """Extract trending hashtags"""
        hashtag_metrics = defaultdict(lambda: {
            'mentions': 0,
            'unique_authors': set(),
            'platforms': set(),
            'total_engagement': 0,
            'sentiment_scores': [],
            'timestamps': [],
            'content_ids': []
        })
        
        # Aggregate hashtag data
        for content in content_batch:
            if not content.hashtags:
                continue
                
            engagement = content.get_engagement_total()
            sentiment = content.sentiment_score or 0.0
            
            for hashtag in content.hashtags:
                metrics = hashtag_metrics[hashtag]
                metrics['mentions'] += 1
                metrics['unique_authors'].add(content.author_id)
                metrics['platforms'].add(content.platform)
                metrics['total_engagement'] += engagement
                metrics['sentiment_scores'].append(sentiment)
                metrics['timestamps'].append(content.created_at)
                metrics['content_ids'].append(content.id)
        
        # Calculate trend metrics for each hashtag
        trends = []
        for hashtag, metrics in hashtag_metrics.items():
            if metrics['mentions'] < self.min_mentions:
                continue
                
            trend_data = await self._calculate_trend_metrics(
                hashtag, metrics, 'hashtag', time_window_hours
            )
            
            if trend_data:
                trends.append(trend_data)
        
        return trends
    
    async def _extract_keyword_trends(self, content_batch: List[Content], 
                                    time_window_hours: int) -> List[Dict[str, Any]]:
        """Extract trending keywords from content text"""
        keyword_metrics = defaultdict(lambda: {
            'mentions': 0,
            'unique_authors': set(),
            'platforms': set(),
            'total_engagement': 0,
            'sentiment_scores': [],
            'timestamps': [],
            'content_ids': []
        })
        
        # Extract keywords from content
        for content in content_batch:
            if not content.keywords:
                continue
                
            engagement = content.get_engagement_total()
            sentiment = content.sentiment_score or 0.0
            
            for keyword in content.keywords:
                # Skip very common words
                if len(keyword) < 3 or keyword.lower() in {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'}:
                    continue
                
                metrics = keyword_metrics[keyword]
                metrics['mentions'] += 1
                metrics['unique_authors'].add(content.author_id)
                metrics['platforms'].add(content.platform)
                metrics['total_engagement'] += engagement
                metrics['sentiment_scores'].append(sentiment)
                metrics['timestamps'].append(content.created_at)
                metrics['content_ids'].append(content.id)
        
        # Calculate trend metrics for each keyword
        trends = []
        for keyword, metrics in keyword_metrics.items():
            if metrics['mentions'] < self.min_mentions:
                continue
                
            trend_data = await self._calculate_trend_metrics(
                keyword, metrics, 'keyword', time_window_hours
            )
            
            if trend_data:
                trends.append(trend_data)
        
        return trends
    
    async def _extract_platform_trends(self, content_batch: List[Content], 
                                     time_window_hours: int) -> List[Dict[str, Any]]:
        """Extract platform-specific trending patterns"""
        platform_trends = []
        
        # Group content by platform
        platform_content = defaultdict(list)
        for content in content_batch:
            platform_content[content.platform].append(content)
        
        # Analyze each platform separately
        for platform, content_list in platform_content.items():
            if len(content_list) < self.min_mentions:
                continue
                
            # Find platform-specific trending topics
            platform_hashtags = defaultdict(int)
            platform_keywords = defaultdict(int)
            
            for content in content_list:
                for hashtag in (content.hashtags or []):
                    platform_hashtags[hashtag] += 1
                for keyword in (content.keywords or []):
                    platform_keywords[keyword] += 1
            
            # Get top trending items for this platform
            top_hashtags = sorted(platform_hashtags.items(), key=lambda x: x[1], reverse=True)[:5]
            top_keywords = sorted(platform_keywords.items(), key=lambda x: x[1], reverse=True)[:5]
            
            if top_hashtags or top_keywords:
                platform_trend = {
                    'keyword': f"{platform}_trending",
                    'type': 'platform_trend',
                    'platform': platform,
                    'mentions': len(content_list),
                    'trend_score': min(len(content_list) / 100, 1.0),
                    'viral_probability': 0.5,
                    'lifecycle_stage': TrendStage.EMERGING.value,
                    'top_hashtags': [item[0] for item in top_hashtags],
                    'top_keywords': [item[0] for item in top_keywords],
                    'velocity': len(content_list) / time_window_hours,
                    'acceleration': 0.0,
                    'engagement_rate': statistics.mean([
                        content.get_engagement_rate() for content in content_list
                        if content.get_engagement_rate() > 0
                    ]) if content_list else 0.0,
                    'geographic_spread': len(set(
                        content.location for content in content_list 
                        if content.location
                    )),
                    'sentiment_score': statistics.mean([
                        content.sentiment_score for content in content_list
                        if content.sentiment_score is not None
                    ]) if content_list else 0.0,
                    'confidence': 0.7,
                    'extracted_at': datetime.now(timezone.utc)
                }
                platform_trends.append(platform_trend)
        
        return platform_trends
    
    async def _calculate_trend_metrics(self, keyword: str, metrics: Dict[str, Any], 
                                     trend_type: str, time_window_hours: int) -> Optional[Dict[str, Any]]:
        """Calculate comprehensive trend metrics for a keyword"""
        try:
            mentions = metrics['mentions']
            unique_authors = len(metrics['unique_authors'])
            platforms = list(metrics['platforms'])
            timestamps = sorted(metrics['timestamps'])
            
            if not timestamps:
                return None
            
            # Calculate velocity (mentions per hour)
            time_span_hours = max(1, (timestamps[-1] - timestamps[0]).total_seconds() / 3600)
            velocity = mentions / time_span_hours
            
            # Calculate acceleration (change in velocity)
            acceleration = await self._calculate_acceleration(keyword, velocity, timestamps)
            
            # Calculate engagement metrics
            total_engagement = metrics['total_engagement']
            avg_engagement = total_engagement / max(mentions, 1)
            engagement_rate = avg_engagement / max(unique_authors, 1) if unique_authors > 0 else 0
            
            # Calculate sentiment
            sentiment_scores = [s for s in metrics['sentiment_scores'] if s is not None]
            avg_sentiment = statistics.mean(sentiment_scores) if sentiment_scores else 0.0
            
            # Calculate geographic spread
            geographic_spread = len(platforms)  # Simplified - could be enhanced with actual location data
            
            # Calculate trend score
            trend_score = self._calculate_trend_score(
                mentions, velocity, acceleration, engagement_rate, geographic_spread
            )
            
            # Calculate viral probability
            viral_probability = self._calculate_viral_probability(
                engagement_rate, velocity, len(platforms), geographic_spread, avg_sentiment
            )
            
            # Determine lifecycle stage
            lifecycle_stage = self._determine_lifecycle_stage(velocity, acceleration, trend_score)
            
            # Calculate confidence
            confidence = self._calculate_confidence(
                mentions, unique_authors, len(platforms), len(timestamps)
            )
            
            return {
                'keyword': keyword,
                'type': trend_type,
                'platform': platforms[0] if platforms else 'unknown',
                'platforms': platforms,
                'mentions': mentions,
                'unique_authors': unique_authors,
                'velocity': velocity,
                'acceleration': acceleration,
                'engagement_rate': engagement_rate,
                'geographic_spread': geographic_spread,
                'sentiment_score': avg_sentiment,
                'trend_score': trend_score,
                'viral_probability': viral_probability,
                'lifecycle_stage': lifecycle_stage,
                'confidence': confidence,
                'time_span_hours': time_span_hours,
                'first_seen': timestamps[0],
                'last_seen': timestamps[-1],
                'content_ids': metrics['content_ids'],
                'extracted_at': datetime.now(timezone.utc)
            }
            
        except Exception as e:
            logger.error(f"Error calculating trend metrics for {keyword}: {e}")
            return None
    
    async def _calculate_acceleration(self, keyword: str, current_velocity: float, 
                                    timestamps: List[datetime]) -> float:
        """Calculate trend acceleration"""
        try:
            # Get historical velocity data (simplified - in production, query database)
            # For now, estimate based on timestamp distribution
            if len(timestamps) < 3:
                return 0.0
            
            # Split timestamps into two halves and compare velocities
            mid_point = len(timestamps) // 2
            first_half = timestamps[:mid_point]
            second_half = timestamps[mid_point:]
            
            if not first_half or not second_half:
                return 0.0
            
            # Calculate velocity for each half
            first_span = (first_half[-1] - first_half[0]).total_seconds() / 3600
            second_span = (second_half[-1] - second_half[0]).total_seconds() / 3600
            
            first_velocity = len(first_half) / max(first_span, 1)
            second_velocity = len(second_half) / max(second_span, 1)
            
            # Calculate acceleration
            time_diff = max(1, (second_half[0] - first_half[-1]).total_seconds() / 3600)
            acceleration = (second_velocity - first_velocity) / time_diff
            
            return acceleration
            
        except Exception as e:
            logger.error(f"Error calculating acceleration for {keyword}: {e}")
            return 0.0
    
    def _calculate_trend_score(self, mentions: int, velocity: float, acceleration: float,
                              engagement_rate: float, geographic_spread: int) -> float:
        """Calculate comprehensive trend score"""
        # Normalize components
        mention_score = min(mentions / 1000, 1.0)
        velocity_score = min(abs(velocity) / 100, 1.0)
        acceleration_score = min(abs(acceleration) / 10, 1.0)
        engagement_score = min(engagement_rate / 10, 1.0)
        geo_score = min(geographic_spread / 5, 1.0)
        
        # Weighted combination
        weights = [0.2, 0.25, 0.2, 0.2, 0.15]
        scores = [mention_score, velocity_score, acceleration_score, engagement_score, geo_score]
        
        weighted_sum = sum(score * weight for score, weight in zip(scores, weights))
        return min(1.0, weighted_sum)
    
    def _calculate_viral_probability(self, engagement_rate: float, velocity: float,
                                   platform_count: int, geo_spread: int, sentiment: float) -> float:
        """Calculate probability of going viral"""
        # Normalize factors
        engagement_score = min(engagement_rate / 10, 1.0)
        velocity_score = min(velocity / 100, 1.0)
        platform_score = min(platform_count / 4, 1.0)
        geo_score = min(geo_spread / 5, 1.0)
        sentiment_score = (sentiment + 1) / 2  # Convert from [-1,1] to [0,1]
        
        # Weighted combination
        weights = [0.3, 0.25, 0.2, 0.15, 0.1]
        scores = [engagement_score, velocity_score, platform_score, geo_score, sentiment_score]
        
        weighted_sum = sum(score * weight for score, weight in zip(scores, weights))
        return min(1.0, weighted_sum)
    
    def _determine_lifecycle_stage(self, velocity: float, acceleration: float, trend_score: float) -> str:
        """Determine trend lifecycle stage"""
        if trend_score < 0.3:
            return TrendStage.EMERGING.value
        elif velocity > 0 and acceleration > 0:
            return TrendStage.GROWING.value
        elif velocity > 0 and acceleration <= 0:
            return TrendStage.PEAK.value
        elif velocity <= 0:
            return TrendStage.DECLINING.value
        else:
            return TrendStage.EMERGING.value
    
    def _calculate_confidence(self, mentions: int, unique_authors: int, 
                            platform_count: int, timestamp_count: int) -> float:
        """Calculate confidence in trend analysis"""
        # Normalize factors
        mention_score = min(mentions / 100, 1.0)
        author_score = min(unique_authors / 50, 1.0)
        platform_score = min(platform_count / 4, 1.0)
        time_score = min(timestamp_count / 50, 1.0)
        
        # Weighted confidence
        weights = [0.4, 0.3, 0.2, 0.1]
        scores = [mention_score, author_score, platform_score, time_score]
        
        confidence = sum(score * weight for score, weight in zip(scores, weights))
        return min(1.0, confidence)
    
    def _rank_trends(self, trends: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Rank trends by importance and relevance"""
        if not trends:
            return []
        
        # Calculate ranking score for each trend
        for trend in trends:
            ranking_score = (
                trend.get('trend_score', 0) * 0.4 +
                trend.get('viral_probability', 0) * 0.3 +
                trend.get('confidence', 0) * 0.2 +
                min(trend.get('mentions', 0) / 100, 1.0) * 0.1
            )
            trend['ranking_score'] = ranking_score
        
        # Sort by ranking score
        ranked_trends = sorted(trends, key=lambda x: x.get('ranking_score', 0), reverse=True)
        
        # Add rank position
        for i, trend in enumerate(ranked_trends):
            trend['rank'] = i + 1
        
        return ranked_trends
    
    async def detect_emerging_trends(self, content_batch: List[Content], 
                                   lookback_hours: int = 6) -> List[Dict[str, Any]]:
        """Detect newly emerging trends"""
        try:
            # Filter recent content
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=lookback_hours)
            recent_content = [
                content for content in content_batch 
                if content.created_at and content.created_at >= cutoff_time
            ]
            
            if not recent_content:
                return []
            
            # Extract trends from recent content
            trends = await self.extract_trends(recent_content, lookback_hours)
            
            # Filter for emerging trends
            emerging_trends = []
            for trend in trends:
                if (trend.get('lifecycle_stage') == TrendStage.EMERGING.value and
                    trend.get('velocity', 0) > 5.0 and  # Minimum velocity threshold
                    trend.get('acceleration', 0) > 0):   # Positive acceleration
                    
                    trend['emergence_score'] = (
                        trend.get('velocity', 0) * 0.4 +
                        trend.get('acceleration', 0) * 0.3 +
                        trend.get('trend_score', 0) * 0.3
                    )
                    emerging_trends.append(trend)
            
            # Sort by emergence score
            emerging_trends.sort(key=lambda x: x.get('emergence_score', 0), reverse=True)
            
            logger.info(f"Detected {len(emerging_trends)} emerging trends")
            return emerging_trends
            
        except Exception as e:
            logger.error(f"Error detecting emerging trends: {e}")
            return []
    
    async def analyze_trend_correlations(self, trends: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze correlations between trends"""
        correlations = []
        
        try:
            # Compare each pair of trends
            for i, trend1 in enumerate(trends):
                for j, trend2 in enumerate(trends[i+1:], i+1):
                    correlation = await self._calculate_trend_correlation(trend1, trend2)
                    if correlation and correlation['correlation_score'] > 0.5:
                        correlations.append(correlation)
            
            # Sort by correlation strength
            correlations.sort(key=lambda x: x['correlation_score'], reverse=True)
            
            return correlations
            
        except Exception as e:
            logger.error(f"Error analyzing trend correlations: {e}")
            return []
    
    async def _calculate_trend_correlation(self, trend1: Dict[str, Any], 
                                         trend2: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Calculate correlation between two trends"""
        try:
            # Check for common platforms
            platforms1 = set(trend1.get('platforms', []))
            platforms2 = set(trend2.get('platforms', []))
            common_platforms = platforms1.intersection(platforms2)
            
            if not common_platforms:
                return None
            
            # Check for temporal overlap
            start1 = trend1.get('first_seen')
            end1 = trend1.get('last_seen')
            start2 = trend2.get('first_seen')
            end2 = trend2.get('last_seen')
            
            if not all([start1, end1, start2, end2]):
                return None
            
            # Calculate overlap
            overlap_start = max(start1, start2)
            overlap_end = min(end1, end2)
            
            if overlap_start >= overlap_end:
                return None
            
            # Calculate correlation score based on multiple factors
            platform_similarity = len(common_platforms) / len(platforms1.union(platforms2))
            
            velocity_similarity = 1 - abs(trend1.get('velocity', 0) - trend2.get('velocity', 0)) / max(
                trend1.get('velocity', 1), trend2.get('velocity', 1), 1
            )
            
            sentiment_similarity = 1 - abs(
                trend1.get('sentiment_score', 0) - trend2.get('sentiment_score', 0)
            ) / 2
            
            # Combined correlation score
            correlation_score = (
                platform_similarity * 0.4 +
                velocity_similarity * 0.3 +
                sentiment_similarity * 0.3
            )
            
            return {
                'keyword1': trend1.get('keyword'),
                'keyword2': trend2.get('keyword'),
                'correlation_score': correlation_score,
                'common_platforms': list(common_platforms),
                'platform_similarity': platform_similarity,
                'velocity_similarity': velocity_similarity,
                'sentiment_similarity': sentiment_similarity,
                'overlap_hours': (overlap_end - overlap_start).total_seconds() / 3600,
                'calculated_at': datetime.now(timezone.utc)
            }
            
        except Exception as e:
            logger.error(f"Error calculating correlation: {e}")
            return None
