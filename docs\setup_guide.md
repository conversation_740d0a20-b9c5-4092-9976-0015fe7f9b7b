# TrendRadar Setup Guide

Complete setup instructions for TrendRadar - Social Media Trend Prediction & Early Warning System.

## Prerequisites

### System Requirements
- **Python**: 3.8 or higher
- **Database**: PostgreSQL 12+ or SQLite (for development)
- **Cache**: Redis 6+ (recommended)
- **Memory**: Minimum 4GB RAM (8GB+ recommended)
- **Storage**: 10GB+ available space

### API Keys Required
Before starting, obtain API keys from:

1. **Twitter API v2** (Free tier available)
   - Visit: https://developer.twitter.com/
   - Apply for developer account
   - Create a new app and get Bearer Token

2. **Reddit API** (Free)
   - Visit: https://www.reddit.com/prefs/apps
   - Create a new application
   - Get Client ID and Client Secret

3. **YouTube Data API v3** (Free tier available)
   - Visit: https://console.cloud.google.com/
   - Enable YouTube Data API v3
   - Create credentials and get API Key

4. **Instagram Basic Display API** (Free)
   - Visit: https://developers.facebook.com/
   - Create a new app
   - Get Access Token

## Installation Methods

### Method 1: Docker Deployment (Recommended)

1. **Clone the repository**
```bash
git clone https://github.com/HectorTa1989/TrendRadar.git
cd TrendRadar
```

2. **Configure environment variables**
```bash
cp .env.example .env
# Edit .env with your API keys and configuration
```

3. **Start with Docker Compose**
```bash
docker-compose up -d
```

4. **Verify deployment**
```bash
curl http://localhost:8000/health
```

### Method 2: Manual Installation

1. **Clone and setup virtual environment**
```bash
git clone https://github.com/HectorTa1989/TrendRadar.git
cd TrendRadar
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
pip install -e .
```

3. **Setup database**
```bash
# Install PostgreSQL and create database
createdb trendradar

# Setup database schema
python scripts/setup_database.py
```

4. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. **Start the application**
```bash
python src/main.py
```

## Configuration

### Environment Variables

Edit your `.env` file with the following configuration:

```env
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/trendradar
REDIS_URL=redis://localhost:6379/0

# Application Settings
SECRET_KEY=your-super-secret-key-change-this-in-production
DEBUG=False
LOG_LEVEL=INFO
ENVIRONMENT=production

# API Keys
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
YOUTUBE_API_KEY=your_youtube_api_key
INSTAGRAM_ACCESS_TOKEN=your_instagram_access_token

# Optional Configuration
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=3600
CACHE_TTL=3600
DATA_RETENTION_DAYS=90
```

### Database Setup

#### PostgreSQL (Recommended)
```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib  # Ubuntu/Debian
brew install postgresql  # macOS

# Create database and user
sudo -u postgres psql
CREATE DATABASE trendradar;
CREATE USER trendradar WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE trendradar TO trendradar;
\q

# Run setup script
python scripts/setup_database.py
```

#### SQLite (Development)
```bash
# For development, use SQLite
export DATABASE_URL=sqlite:///./trendradar.db
python scripts/setup_database.py
```

### Redis Setup
```bash
# Install Redis
sudo apt-get install redis-server  # Ubuntu/Debian
brew install redis  # macOS

# Start Redis
redis-server

# Test connection
redis-cli ping
```

## API Configuration

### Twitter API Setup
1. Go to https://developer.twitter.com/
2. Apply for a developer account
3. Create a new project/app
4. Generate Bearer Token
5. Add to `.env`: `TWITTER_BEARER_TOKEN=your_token`

### Reddit API Setup
1. Go to https://www.reddit.com/prefs/apps
2. Click "Create App" or "Create Another App"
3. Choose "script" type
4. Get Client ID and Secret
5. Add to `.env`:
   ```
   REDDIT_CLIENT_ID=your_client_id
   REDDIT_CLIENT_SECRET=your_client_secret
   ```

### YouTube API Setup
1. Go to https://console.cloud.google.com/
2. Create a new project or select existing
3. Enable YouTube Data API v3
4. Create credentials (API Key)
5. Add to `.env`: `YOUTUBE_API_KEY=your_api_key`

### Instagram API Setup
1. Go to https://developers.facebook.com/
2. Create a new app
3. Add Instagram Basic Display product
4. Generate Access Token
5. Add to `.env`: `INSTAGRAM_ACCESS_TOKEN=your_token`

## Running the Application

### Development Mode
```bash
# Activate virtual environment
source venv/bin/activate

# Start with auto-reload
python src/main.py
# or
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

### Production Mode
```bash
# Using Docker Compose (recommended)
docker-compose up -d

# Or manual with Gunicorn
gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### Background Services
```bash
# Start Celery worker (for background tasks)
celery -A src.main worker --loglevel=info

# Start Celery beat (for scheduled tasks)
celery -A src.main beat --loglevel=info
```

## Verification

### Health Check
```bash
curl http://localhost:8000/health
```

Expected response:
```json
{
  "status": "healthy",
  "components": {
    "database": "healthy",
    "redis": "healthy",
    "data_collection": "healthy",
    "alert_system": "healthy"
  }
}
```

### API Endpoints
```bash
# Get trending topics
curl http://localhost:8000/api/v1/trends

# Get predictions
curl http://localhost:8000/api/v1/predictions

# Get platform status
curl http://localhost:8000/api/v1/platforms/status
```

### Web Interface
- **API Documentation**: http://localhost:8000/docs
- **Alternative Docs**: http://localhost:8000/redoc
- **Monitoring**: http://localhost:3000 (Grafana)
- **Metrics**: http://localhost:9090 (Prometheus)

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   ```bash
   # Check PostgreSQL is running
   sudo systemctl status postgresql
   
   # Check connection
   psql -h localhost -U trendradar -d trendradar
   ```

2. **Redis Connection Error**
   ```bash
   # Check Redis is running
   redis-cli ping
   
   # Start Redis if not running
   redis-server
   ```

3. **API Rate Limits**
   - Check API key validity
   - Monitor rate limit headers in logs
   - Adjust collection intervals if needed

4. **Memory Issues**
   ```bash
   # Monitor memory usage
   docker stats  # For Docker deployment
   htop  # For manual deployment
   ```

### Logs
```bash
# Docker logs
docker-compose logs -f trendradar

# Manual deployment logs
tail -f logs/trendradar.log
```

### Performance Tuning

1. **Database Optimization**
   ```sql
   -- Add indexes for better performance
   CREATE INDEX CONCURRENTLY idx_trends_keyword_platform ON trends(keyword, platform);
   CREATE INDEX CONCURRENTLY idx_content_created_at_platform ON content(created_at, platform);
   ```

2. **Redis Configuration**
   ```bash
   # Increase memory limit
   redis-cli CONFIG SET maxmemory 1gb
   redis-cli CONFIG SET maxmemory-policy allkeys-lru
   ```

3. **Application Tuning**
   ```env
   # Adjust in .env
   API_WORKERS=4
   BATCH_SIZE=100
   CACHE_TTL=1800
   ```

## Security Considerations

1. **API Keys**: Never commit API keys to version control
2. **Database**: Use strong passwords and limit access
3. **Network**: Use HTTPS in production
4. **Updates**: Keep dependencies updated

## Monitoring and Maintenance

### Monitoring Setup
```bash
# Access Grafana dashboard
http://localhost:3000
# Default login: admin/admin123

# Access Prometheus metrics
http://localhost:9090
```

### Backup
```bash
# Database backup
pg_dump trendradar > backup_$(date +%Y%m%d).sql

# Restore
psql trendradar < backup_20231201.sql
```

### Updates
```bash
# Pull latest changes
git pull origin main

# Update dependencies
pip install -r requirements.txt

# Run migrations if needed
python scripts/setup_database.py --verify-only
```

## Support

- **Documentation**: Check `/docs` directory
- **Issues**: Report at GitHub Issues
- **API Reference**: http://localhost:8000/docs
- **Logs**: Check application logs for detailed error information

For additional help, refer to the troubleshooting section or create an issue on GitHub.
