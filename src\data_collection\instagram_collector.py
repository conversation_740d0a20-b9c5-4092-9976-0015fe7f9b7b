"""
Instagram Data Collector for TrendRadar
Implements Instagram Basic Display API integration for trend data collection
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
import aiohttp
import json

from config.api_keys import get_instagram_auth
from src.data_collection.base_collector import BaseCollector, CollectedContent
from src.utils.helpers import extract_hashtags, extract_mentions, extract_urls, clean_text
from src.utils.constants import Platform, ContentType

logger = logging.getLogger(__name__)


class InstagramCollector(BaseCollector):
    """Instagram Basic Display API data collector"""
    
    def __init__(self, platform: Platform = Platform.INSTAGRAM):
        super().__init__(platform)
        self.base_url = "https://graph.instagram.com"
        self.access_token = None
        self.session = None
    
    async def initialize(self) -> bool:
        """Initialize Instagram API connection"""
        try:
            auth_config = get_instagram_auth()
            if not auth_config:
                logger.error("Instagram authentication not configured")
                return False
            
            self.access_token = auth_config["access_token"]
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            # Test API connection
            test_url = f"{self.base_url}/me"
            params = {
                "fields": "id,username",
                "access_token": self.access_token
            }
            
            async with self.session.get(test_url, params=params) as response:
                if response.status == 200:
                    logger.info("Instagram API connection successful")
                    return True
                else:
                    logger.error(f"Instagram API test failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to initialize Instagram collector: {e}")
            return False
    
    async def collect_trending_topics(self, limit: int = 50) -> List[str]:
        """Collect trending topics from Instagram"""
        try:
            # Instagram Basic Display API has limited access to trending data
            # We'll extract hashtags from user's own media as a starting point
            url = f"{self.base_url}/me/media"
            params = {
                "fields": "caption,media_type,timestamp",
                "limit": 100,
                "access_token": self.access_token
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "data" not in result:
                return []
            
            trending_topics = set()
            
            for media in result["data"]:
                caption = media.get("caption", "")
                if caption:
                    hashtags = extract_hashtags(caption)
                    trending_topics.update(hashtags)
            
            # Since we have limited access, we'll add some common trending topics
            common_topics = [
                "instagram", "photo", "selfie", "love", "instagood", 
                "photooftheday", "fashion", "beautiful", "happy", "cute",
                "tbt", "followme", "picoftheday", "follow", "me", "summer",
                "instadaily", "friends", "repost", "nature"
            ]
            trending_topics.update(common_topics)
            
            return list(trending_topics)[:limit]
            
        except Exception as e:
            logger.error(f"Error collecting Instagram trending topics: {e}")
            return []
    
    async def search_content(self, query: str, limit: int = 100) -> List[CollectedContent]:
        """Search for Instagram content based on query"""
        try:
            # Instagram Basic Display API doesn't support search
            # We'll search through user's own media for the query
            url = f"{self.base_url}/me/media"
            params = {
                "fields": "id,caption,media_type,media_url,thumbnail_url,timestamp,permalink",
                "limit": min(limit, 100),
                "access_token": self.access_token
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "data" not in result:
                return []
            
            content_list = []
            query_lower = query.lower()
            
            for media in result["data"]:
                try:
                    caption = media.get("caption", "").lower()
                    if query_lower in caption or f"#{query_lower}" in caption:
                        content = self._parse_instagram_media(media)
                        if content:
                            content_list.append(content)
                except Exception as e:
                    logger.error(f"Error parsing Instagram media: {e}")
                    continue
            
            return content_list
            
        except Exception as e:
            logger.error(f"Error searching Instagram content for '{query}': {e}")
            return []
    
    async def get_user_content(self, user_id: str, limit: int = 50) -> List[CollectedContent]:
        """Get recent media from Instagram user (limited to authenticated user)"""
        try:
            # Instagram Basic Display API only allows access to user's own media
            url = f"{self.base_url}/me/media"
            params = {
                "fields": "id,caption,media_type,media_url,thumbnail_url,timestamp,permalink",
                "limit": min(limit, 100),
                "access_token": self.access_token
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "data" not in result:
                return []
            
            content_list = []
            for media in result["data"]:
                try:
                    content = self._parse_instagram_media(media)
                    if content:
                        content_list.append(content)
                except Exception as e:
                    logger.error(f"Error parsing Instagram user media: {e}")
                    continue
            
            return content_list
            
        except Exception as e:
            logger.error(f"Error getting Instagram user content: {e}")
            return []
    
    async def get_content_details(self, content_id: str) -> Optional[CollectedContent]:
        """Get detailed information about a specific Instagram media"""
        try:
            url = f"{self.base_url}/{content_id}"
            params = {
                "fields": "id,caption,media_type,media_url,thumbnail_url,timestamp,permalink",
                "access_token": self.access_token
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result:
                return None
            
            return self._parse_instagram_media(result)
            
        except Exception as e:
            logger.error(f"Error getting Instagram content details for {content_id}: {e}")
            return None
    
    async def get_user_info(self) -> Optional[Dict[str, Any]]:
        """Get authenticated user information"""
        try:
            url = f"{self.base_url}/me"
            params = {
                "fields": "id,username,media_count",
                "access_token": self.access_token
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            return result
            
        except Exception as e:
            logger.error(f"Error getting Instagram user info: {e}")
            return None
    
    async def _make_api_request(self, url: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Make API request to Instagram"""
        if not self.session or not self.access_token:
            logger.error("Instagram session or access token not initialized")
            return None
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:
                    logger.warning("Instagram rate limit exceeded")
                    self.metrics.rate_limited_requests += 1
                    return None
                elif response.status == 400:
                    error_data = await response.json()
                    if "access_token" in str(error_data):
                        logger.error("Instagram access token invalid or expired")
                    else:
                        logger.error(f"Instagram API bad request: {error_data}")
                    return None
                else:
                    logger.error(f"Instagram API error: {response.status} - {await response.text()}")
                    return None
                    
        except Exception as e:
            logger.error(f"Instagram API request failed: {e}")
            return None
    
    def _parse_instagram_media(self, media_data: Dict[str, Any]) -> Optional[CollectedContent]:
        """Parse Instagram API response into CollectedContent"""
        try:
            media_id = media_data.get("id")
            if not media_id:
                return None
            
            caption = media_data.get("caption", "")
            media_type = media_data.get("media_type", "IMAGE")
            media_url = media_data.get("media_url", "")
            thumbnail_url = media_data.get("thumbnail_url", "")
            permalink = media_data.get("permalink", "")
            
            # Parse timestamps
            timestamp_str = media_data.get("timestamp", "")
            created_at = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')) if timestamp_str else datetime.now(timezone.utc)
            
            # Instagram Basic Display API doesn't provide engagement metrics
            # These would need to be collected through other means
            engagement_metrics = {
                "likes": 0,  # Not available in Basic Display API
                "comments": 0,  # Not available in Basic Display API
                "shares": 0,  # Not available in Basic Display API
                "saves": 0  # Not available in Basic Display API
            }
            
            # Extract text elements
            hashtags = extract_hashtags(caption)
            mentions = extract_mentions(caption)
            urls = extract_urls(caption)
            
            # Add permalink to URLs
            if permalink:
                urls.append(permalink)
            
            # Determine content type and media URLs
            content_type = ContentType.IMAGE.value
            media_urls = []
            
            if media_type == "VIDEO":
                content_type = ContentType.VIDEO.value
                if media_url:
                    media_urls.append(media_url)
                if thumbnail_url:
                    media_urls.append(thumbnail_url)
            elif media_type == "IMAGE":
                content_type = ContentType.IMAGE.value
                if media_url:
                    media_urls.append(media_url)
            elif media_type == "CAROUSEL_ALBUM":
                content_type = ContentType.IMAGE.value  # Mixed content, default to image
                if media_url:
                    media_urls.append(media_url)
            
            return CollectedContent(
                id=media_id,
                platform=Platform.INSTAGRAM,
                content_type=content_type,
                text=clean_text(caption),
                author_id="me",  # Basic Display API only shows user's own content
                author_username="me",
                created_at=created_at,
                engagement_metrics=engagement_metrics,
                hashtags=hashtags,
                mentions=mentions,
                urls=urls,
                location=None,  # Location not available in Basic Display API
                language="en",  # Language detection would need to be implemented
                media_urls=media_urls,
                parent_id=None,
                raw_data=media_data
            )
            
        except Exception as e:
            logger.error(f"Error parsing Instagram media data: {e}")
            return None
    
    async def close(self):
        """Close the HTTP session"""
        if self.session:
            await self.session.close()
    
    def __del__(self):
        """Cleanup on deletion"""
        if self.session and not self.session.closed:
            asyncio.create_task(self.session.close())
