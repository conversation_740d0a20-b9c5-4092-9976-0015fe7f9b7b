"""
TrendRadar Main Application
FastAPI-based social media trend prediction and early warning system
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from config.settings import settings
from config.database import startup_database, shutdown_database
from src.api.routes import router as api_router
from src.api.middleware import RateLimitMiddleware, LoggingMiddleware
from src.utils.helpers import setup_logging
from src.alerts.alert_manager import AlertManager
from src.data_collection.base_collector import DataCollectionManager

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    # Startup
    logger.info("Starting TrendRadar application...")
    
    try:
        # Initialize database connections
        await startup_database()
        
        # Initialize data collection manager
        data_manager = DataCollectionManager()
        await data_manager.initialize()
        app.state.data_manager = data_manager
        
        # Initialize alert manager
        alert_manager = AlertManager()
        await alert_manager.initialize()
        app.state.alert_manager = alert_manager
        
        # Start background tasks
        asyncio.create_task(data_manager.start_collection())
        asyncio.create_task(alert_manager.start_monitoring())
        
        logger.info("TrendRadar application started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down TrendRadar application...")
    
    try:
        # Stop background tasks
        if hasattr(app.state, 'data_manager'):
            await app.state.data_manager.stop_collection()
        
        if hasattr(app.state, 'alert_manager'):
            await app.state.alert_manager.stop_monitoring()
        
        # Close database connections
        await shutdown_database()
        
        logger.info("TrendRadar application shut down successfully")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI application
app = FastAPI(
    title="TrendRadar",
    description="Advanced Social Media Trend Prediction & Early Warning System",
    version="1.0.0",
    docs_url="/docs" if settings.enable_swagger else None,
    redoc_url="/redoc" if settings.enable_redoc else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.debug else ["localhost", "127.0.0.1"]
)

app.add_middleware(RateLimitMiddleware)
app.add_middleware(LoggingMiddleware)

# Include API routes
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    """Root endpoint with application information"""
    return {
        "name": "TrendRadar",
        "version": "1.0.0",
        "description": "Advanced Social Media Trend Prediction & Early Warning System",
        "status": "running",
        "docs": "/docs" if settings.enable_swagger else None
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        from config.database import DatabaseManager
        
        # Check database connection
        db_healthy = await DatabaseManager.check_connection()
        redis_healthy = await DatabaseManager.check_redis_connection()
        
        # Check data collection status
        data_collection_healthy = True
        if hasattr(app.state, 'data_manager'):
            data_collection_healthy = app.state.data_manager.is_healthy()
        
        # Check alert system status
        alert_system_healthy = True
        if hasattr(app.state, 'alert_manager'):
            alert_system_healthy = app.state.alert_manager.is_healthy()
        
        overall_health = all([
            db_healthy,
            redis_healthy,
            data_collection_healthy,
            alert_system_healthy
        ])
        
        return {
            "status": "healthy" if overall_health else "unhealthy",
            "components": {
                "database": "healthy" if db_healthy else "unhealthy",
                "redis": "healthy" if redis_healthy else "unhealthy",
                "data_collection": "healthy" if data_collection_healthy else "unhealthy",
                "alert_system": "healthy" if alert_system_healthy else "unhealthy"
            },
            "timestamp": asyncio.get_event_loop().time()
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": asyncio.get_event_loop().time()
            }
        )


@app.get("/metrics")
async def metrics():
    """Metrics endpoint for monitoring"""
    if not settings.enable_metrics:
        raise HTTPException(status_code=404, detail="Metrics disabled")
    
    try:
        metrics_data = {
            "system": {
                "uptime": asyncio.get_event_loop().time(),
                "memory_usage": "N/A",  # Can be implemented with psutil
                "cpu_usage": "N/A"
            },
            "data_collection": {},
            "trends": {},
            "alerts": {}
        }
        
        # Get data collection metrics
        if hasattr(app.state, 'data_manager'):
            metrics_data["data_collection"] = await app.state.data_manager.get_metrics()
        
        # Get alert metrics
        if hasattr(app.state, 'alert_manager'):
            metrics_data["alerts"] = await app.state.alert_manager.get_metrics()
        
        return metrics_data
        
    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        raise HTTPException(status_code=500, detail="Failed to collect metrics")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": str(exc) if settings.debug else "An unexpected error occurred",
            "type": type(exc).__name__
        }
    )


class TrendRadarApplication:
    """Main application class for TrendRadar"""
    
    def __init__(self):
        self.app = app
        self.data_manager = None
        self.alert_manager = None
    
    async def initialize(self):
        """Initialize the application components"""
        logger.info("Initializing TrendRadar components...")
        
        # This will be called during lifespan startup
        pass
    
    async def start_background_tasks(self):
        """Start background processing tasks"""
        logger.info("Starting background tasks...")
        
        # Data collection task
        if self.data_manager:
            asyncio.create_task(self.data_manager.start_collection())
        
        # Alert monitoring task
        if self.alert_manager:
            asyncio.create_task(self.alert_manager.start_monitoring())
        
        # Trend analysis task
        asyncio.create_task(self._trend_analysis_loop())
        
        # Data cleanup task
        asyncio.create_task(self._cleanup_loop())
    
    async def _trend_analysis_loop(self):
        """Background task for continuous trend analysis"""
        while True:
            try:
                # Perform trend analysis
                # This will be implemented when predictive analytics is ready
                await asyncio.sleep(300)  # Run every 5 minutes
                
            except Exception as e:
                logger.error(f"Trend analysis loop error: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _cleanup_loop(self):
        """Background task for data cleanup"""
        while True:
            try:
                # Perform data cleanup based on retention policy
                from config.database import DatabaseUtils
                await DatabaseUtils.cleanup_old_data(settings.data_retention_days)
                
                # Wait for next cleanup cycle
                await asyncio.sleep(settings.cleanup_interval_hours * 3600)
                
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour before retrying


def create_app() -> FastAPI:
    """Factory function to create FastAPI application"""
    return app


if __name__ == "__main__":
    # Run the application
    uvicorn.run(
        "src.main:app",
        host=settings.api_host,
        port=settings.api_port,
        workers=1 if settings.debug else settings.api_workers,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )
