"""
Twitter Data Collector for TrendRadar
Implements Twitter API v2 integration for trend data collection
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
import aiohttp
import json

from config.api_keys import get_twitter_auth
from src.data_collection.base_collector import <PERSON>Collector, CollectedContent
from src.utils.helpers import extract_hashtags, extract_mentions, extract_urls, clean_text
from src.utils.constants import Platform, ContentType

logger = logging.getLogger(__name__)


class TwitterCollector(BaseCollector):
    """Twitter API v2 data collector"""
    
    def __init__(self, platform: Platform = Platform.TWITTER):
        super().__init__(platform)
        self.base_url = "https://api.twitter.com/2"
        self.auth_headers = {}
        self.session = None
    
    async def initialize(self) -> bool:
        """Initialize Twitter API connection"""
        try:
            auth_config = get_twitter_auth()
            if not auth_config:
                logger.error("Twitter authentication not configured")
                return False
            
            # Set up authentication headers
            if "bearer_token" in auth_config:
                self.auth_headers = {
                    "Authorization": f"Bearer {auth_config['bearer_token']}",
                    "Content-Type": "application/json"
                }
            else:
                # OAuth 1.0a authentication would be implemented here
                logger.error("OAuth 1.0a authentication not implemented yet")
                return False
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                headers=self.auth_headers,
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            # Test API connection
            test_url = f"{self.base_url}/tweets/search/recent"
            params = {
                "query": "test",
                "max_results": 10,
                "tweet.fields": "created_at,author_id,public_metrics"
            }
            
            async with self.session.get(test_url, params=params) as response:
                if response.status == 200:
                    logger.info("Twitter API connection successful")
                    return True
                else:
                    logger.error(f"Twitter API test failed: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to initialize Twitter collector: {e}")
            return False
    
    async def collect_trending_topics(self, limit: int = 50) -> List[str]:
        """Collect trending topics from Twitter"""
        try:
            # Twitter API v2 doesn't have a direct trends endpoint in the free tier
            # We'll use search to find popular hashtags instead
            trending_queries = [
                "#trending", "#viral", "#breaking", "#news", "#popular"
            ]
            
            hashtags = set()
            
            for query in trending_queries:
                url = f"{self.base_url}/tweets/search/recent"
                params = {
                    "query": f"{query} -is:retweet",
                    "max_results": 100,
                    "tweet.fields": "created_at,public_metrics",
                    "expansions": "author_id"
                }
                
                result = await self.make_request(self._make_api_request, url, params)
                if result and "data" in result:
                    for tweet in result["data"]:
                        tweet_hashtags = extract_hashtags(tweet.get("text", ""))
                        hashtags.update(tweet_hashtags)
            
            # Sort by frequency (simplified - in production, use engagement metrics)
            trending_topics = list(hashtags)[:limit]
            return trending_topics
            
        except Exception as e:
            logger.error(f"Error collecting Twitter trending topics: {e}")
            return []
    
    async def search_content(self, query: str, limit: int = 100) -> List[CollectedContent]:
        """Search for Twitter content based on query"""
        try:
            url = f"{self.base_url}/tweets/search/recent"
            params = {
                "query": f"{query} -is:retweet lang:en",
                "max_results": min(limit, 100),  # API limit
                "tweet.fields": "created_at,author_id,public_metrics,geo,lang,context_annotations",
                "user.fields": "username,public_metrics,location",
                "expansions": "author_id,geo.place_id"
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "data" not in result:
                return []
            
            # Process users data for username lookup
            users_data = {}
            if "includes" in result and "users" in result["includes"]:
                for user in result["includes"]["users"]:
                    users_data[user["id"]] = user
            
            content_list = []
            for tweet in result["data"]:
                try:
                    content = self._parse_tweet(tweet, users_data)
                    if content:
                        content_list.append(content)
                except Exception as e:
                    logger.error(f"Error parsing tweet {tweet.get('id', 'unknown')}: {e}")
                    continue
            
            return content_list
            
        except Exception as e:
            logger.error(f"Error searching Twitter content for '{query}': {e}")
            return []
    
    async def get_user_content(self, user_id: str, limit: int = 50) -> List[CollectedContent]:
        """Get recent tweets from a specific user"""
        try:
            url = f"{self.base_url}/users/{user_id}/tweets"
            params = {
                "max_results": min(limit, 100),
                "tweet.fields": "created_at,public_metrics,geo,lang,context_annotations",
                "user.fields": "username,public_metrics,location",
                "expansions": "author_id"
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "data" not in result:
                return []
            
            # Process users data
            users_data = {}
            if "includes" in result and "users" in result["includes"]:
                for user in result["includes"]["users"]:
                    users_data[user["id"]] = user
            
            content_list = []
            for tweet in result["data"]:
                try:
                    content = self._parse_tweet(tweet, users_data)
                    if content:
                        content_list.append(content)
                except Exception as e:
                    logger.error(f"Error parsing user tweet {tweet.get('id', 'unknown')}: {e}")
                    continue
            
            return content_list
            
        except Exception as e:
            logger.error(f"Error getting Twitter user content for {user_id}: {e}")
            return []
    
    async def get_content_details(self, content_id: str) -> Optional[CollectedContent]:
        """Get detailed information about a specific tweet"""
        try:
            url = f"{self.base_url}/tweets/{content_id}"
            params = {
                "tweet.fields": "created_at,author_id,public_metrics,geo,lang,context_annotations,referenced_tweets",
                "user.fields": "username,public_metrics,location",
                "expansions": "author_id,referenced_tweets.id"
            }
            
            result = await self.make_request(self._make_api_request, url, params)
            if not result or "data" not in result:
                return None
            
            # Process users data
            users_data = {}
            if "includes" in result and "users" in result["includes"]:
                for user in result["includes"]["users"]:
                    users_data[user["id"]] = user
            
            return self._parse_tweet(result["data"], users_data)
            
        except Exception as e:
            logger.error(f"Error getting Twitter content details for {content_id}: {e}")
            return None
    
    async def _make_api_request(self, url: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Make API request to Twitter"""
        if not self.session:
            logger.error("Twitter session not initialized")
            return None
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:
                    logger.warning("Twitter rate limit exceeded")
                    self.metrics.rate_limited_requests += 1
                    return None
                else:
                    logger.error(f"Twitter API error: {response.status} - {await response.text()}")
                    return None
                    
        except Exception as e:
            logger.error(f"Twitter API request failed: {e}")
            return None
    
    def _parse_tweet(self, tweet_data: Dict[str, Any], users_data: Dict[str, Any]) -> Optional[CollectedContent]:
        """Parse Twitter API response into CollectedContent"""
        try:
            tweet_id = tweet_data.get("id")
            if not tweet_id:
                return None
            
            text = tweet_data.get("text", "")
            author_id = tweet_data.get("author_id", "")
            
            # Get user information
            user_info = users_data.get(author_id, {})
            username = user_info.get("username", "unknown")
            
            # Parse timestamps
            created_at_str = tweet_data.get("created_at")
            created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00')) if created_at_str else datetime.now(timezone.utc)
            
            # Extract engagement metrics
            public_metrics = tweet_data.get("public_metrics", {})
            engagement_metrics = {
                "likes": public_metrics.get("like_count", 0),
                "retweets": public_metrics.get("retweet_count", 0),
                "replies": public_metrics.get("reply_count", 0),
                "quotes": public_metrics.get("quote_count", 0)
            }
            
            # Extract text elements
            hashtags = extract_hashtags(text)
            mentions = extract_mentions(text)
            urls = extract_urls(text)
            
            # Get location if available
            location = None
            if "geo" in tweet_data and tweet_data["geo"]:
                location = tweet_data["geo"].get("place_id")
            elif user_info.get("location"):
                location = user_info["location"]
            
            # Determine content type
            content_type = ContentType.TEXT.value
            media_urls = []
            
            # Check for referenced tweets (replies, retweets, quotes)
            parent_id = None
            if "referenced_tweets" in tweet_data:
                for ref in tweet_data["referenced_tweets"]:
                    if ref.get("type") == "replied_to":
                        parent_id = ref.get("id")
                        break
            
            return CollectedContent(
                id=tweet_id,
                platform=Platform.TWITTER,
                content_type=content_type,
                text=clean_text(text),
                author_id=author_id,
                author_username=username,
                created_at=created_at,
                engagement_metrics=engagement_metrics,
                hashtags=hashtags,
                mentions=mentions,
                urls=urls,
                location=location,
                language=tweet_data.get("lang", "en"),
                media_urls=media_urls,
                parent_id=parent_id,
                raw_data=tweet_data
            )
            
        except Exception as e:
            logger.error(f"Error parsing tweet data: {e}")
            return None
    
    async def close(self):
        """Close the HTTP session"""
        if self.session:
            await self.session.close()
    
    def __del__(self):
        """Cleanup on deletion"""
        if self.session and not self.session.closed:
            asyncio.create_task(self.session.close())
