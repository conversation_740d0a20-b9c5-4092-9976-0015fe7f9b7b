"""
Middleware for TrendRadar API
Custom middleware for rate limiting, logging, and request processing
"""

import time
import logging
from typing import Callable
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import <PERSON>GI<PERSON>pp
import asyncio
from collections import defaultdict, deque

from config.settings import settings
from src.utils.helpers import RateLimiter

logger = logging.getLogger(__name__)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.rate_limiters = defaultdict(lambda: RateLimiter(
            max_requests=settings.rate_limit_requests,
            window_seconds=settings.rate_limit_window
        ))
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Get client IP
        client_ip = request.client.host
        
        # Check rate limit
        rate_limiter = self.rate_limiters[client_ip]
        
        if not rate_limiter.is_allowed():
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests. Try again in {rate_limiter.time_until_reset():.1f} seconds",
                    "retry_after": rate_limiter.time_until_reset()
                },
                headers={
                    "Retry-After": str(int(rate_limiter.time_until_reset()))
                }
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        response.headers["X-RateLimit-Limit"] = str(settings.rate_limit_requests)
        response.headers["X-RateLimit-Window"] = str(settings.rate_limit_window)
        response.headers["X-RateLimit-Remaining"] = str(
            settings.rate_limit_requests - len(rate_limiter.requests)
        )
        
        return response


class LoggingMiddleware(BaseHTTPMiddleware):
    """Request/response logging middleware"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.request_times = deque(maxlen=1000)  # Store recent request times
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # Log request
        logger.info(
            f"Request: {request.method} {request.url.path} "
            f"from {request.client.host}"
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            self.request_times.append(process_time)
            
            # Log response
            logger.info(
                f"Response: {response.status_code} "
                f"in {process_time:.3f}s"
            )
            
            # Add timing headers
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            
            logger.error(
                f"Request failed: {request.method} {request.url.path} "
                f"in {process_time:.3f}s - {str(e)}"
            )
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Internal server error",
                    "message": "An unexpected error occurred"
                }
            )
    
    def get_average_response_time(self) -> float:
        """Get average response time"""
        if not self.request_times:
            return 0.0
        return sum(self.request_times) / len(self.request_times)


class CORSMiddleware(BaseHTTPMiddleware):
    """Custom CORS middleware"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.allowed_origins = settings.cors_origins
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        origin = request.headers.get("origin")
        
        # Handle preflight requests
        if request.method == "OPTIONS":
            response = Response()
            if origin in self.allowed_origins or "*" in self.allowed_origins:
                response.headers["Access-Control-Allow-Origin"] = origin
                response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
                response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
                response.headers["Access-Control-Max-Age"] = "86400"
            return response
        
        # Process request
        response = await call_next(request)
        
        # Add CORS headers
        if origin in self.allowed_origins or "*" in self.allowed_origins:
            response.headers["Access-Control-Allow-Origin"] = origin
            response.headers["Access-Control-Allow-Credentials"] = "true"
        
        return response


class SecurityMiddleware(BaseHTTPMiddleware):
    """Security headers middleware"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        if not settings.debug:
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response


class MetricsMiddleware(BaseHTTPMiddleware):
    """Metrics collection middleware"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.request_count = defaultdict(int)
        self.response_times = defaultdict(list)
        self.error_count = defaultdict(int)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        endpoint = f"{request.method} {request.url.path}"
        
        # Increment request count
        self.request_count[endpoint] += 1
        
        try:
            response = await call_next(request)
            
            # Record response time
            process_time = time.time() - start_time
            self.response_times[endpoint].append(process_time)
            
            # Keep only recent response times (last 100)
            if len(self.response_times[endpoint]) > 100:
                self.response_times[endpoint] = self.response_times[endpoint][-100:]
            
            return response
            
        except Exception as e:
            # Increment error count
            self.error_count[endpoint] += 1
            raise
    
    def get_metrics(self) -> dict:
        """Get collected metrics"""
        metrics = {
            "requests": dict(self.request_count),
            "errors": dict(self.error_count),
            "avg_response_times": {}
        }
        
        for endpoint, times in self.response_times.items():
            if times:
                metrics["avg_response_times"][endpoint] = sum(times) / len(times)
        
        return metrics


class RequestValidationMiddleware(BaseHTTPMiddleware):
    """Request validation middleware"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.max_request_size = 10 * 1024 * 1024  # 10MB
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check request size
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.max_request_size:
            return JSONResponse(
                status_code=413,
                content={
                    "error": "Request too large",
                    "message": f"Request size exceeds {self.max_request_size} bytes"
                }
            )
        
        # Validate content type for POST/PUT requests
        if request.method in ["POST", "PUT"]:
            content_type = request.headers.get("content-type", "")
            if not content_type.startswith(("application/json", "multipart/form-data")):
                return JSONResponse(
                    status_code=415,
                    content={
                        "error": "Unsupported media type",
                        "message": "Content-Type must be application/json or multipart/form-data"
                    }
                )
        
        return await call_next(request)


class CacheMiddleware(BaseHTTPMiddleware):
    """Response caching middleware"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.cache = {}
        self.cache_ttl = 300  # 5 minutes
        self.cacheable_methods = ["GET"]
        self.cacheable_paths = ["/api/v1/trends", "/api/v1/predictions", "/api/v1/analytics"]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check if request is cacheable
        if (request.method not in self.cacheable_methods or 
            not any(request.url.path.startswith(path) for path in self.cacheable_paths)):
            return await call_next(request)
        
        # Generate cache key
        cache_key = f"{request.method}:{request.url.path}:{request.url.query}"
        
        # Check cache
        if cache_key in self.cache:
            cached_response, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                logger.debug(f"Cache hit for {cache_key}")
                response = Response(
                    content=cached_response["content"],
                    status_code=cached_response["status_code"],
                    headers=cached_response["headers"]
                )
                response.headers["X-Cache"] = "HIT"
                return response
        
        # Process request
        response = await call_next(request)
        
        # Cache successful responses
        if response.status_code == 200:
            # Read response content
            content = b""
            async for chunk in response.body_iterator:
                content += chunk
            
            # Store in cache
            self.cache[cache_key] = (
                {
                    "content": content,
                    "status_code": response.status_code,
                    "headers": dict(response.headers)
                },
                time.time()
            )
            
            # Create new response
            response = Response(
                content=content,
                status_code=response.status_code,
                headers=response.headers
            )
            response.headers["X-Cache"] = "MISS"
        
        return response
    
    def clear_cache(self):
        """Clear the cache"""
        self.cache.clear()
        logger.info("Cache cleared")


# Global middleware instances
rate_limit_middleware = None
logging_middleware = None
metrics_middleware = None


def get_rate_limit_middleware():
    """Get rate limit middleware instance"""
    global rate_limit_middleware
    if rate_limit_middleware is None:
        rate_limit_middleware = RateLimitMiddleware
    return rate_limit_middleware


def get_logging_middleware():
    """Get logging middleware instance"""
    global logging_middleware
    if logging_middleware is None:
        logging_middleware = LoggingMiddleware
    return logging_middleware


def get_metrics_middleware():
    """Get metrics middleware instance"""
    global metrics_middleware
    if metrics_middleware is None:
        metrics_middleware = MetricsMiddleware
    return metrics_middleware
